import type { Meta, StoryObj } from '@storybook/react';

import { ApprovalInbox } from './ApprovalInbox';
import { BuildMyCareerPath } from './BuildMyCareerPath';
import { EOYSelfAssessment } from './EOYSelfAssessment';
import { GiveFeedback } from './GiveFeedback';
import { MyFeedback } from './MyFeedback';
import { RatingAuditHistory } from './RatingAuditHistory';
import { RequestFeedback } from './RequestFeedback';
import { ResumeLerning } from './ResumeLerning';

const meta = {
  title: 'Examples/Desktop/Pages',
  component: MyFeedback,
  tags: ['autodocs'],
  parameters: {},
  args: {},
  argTypes: {}
} satisfies Meta<typeof MyFeedback>;

export default meta;
type Story = StoryObj<typeof meta>;

export const EOYSelfAssessmentPage: Story = {
  name: 'EOY Self Assessment',
  render: EOYSelfAssessment
};

export const MyFeedbackPage: Story = {
  name: 'My Feedback',
  render: MyFeedback
};

export const RequestFeedbackPage: Story = {
  name: 'Request Feedback',
  render: RequestFeedback
};

export const GiveFeedbackPage: Story = {
  name: 'Give Feedback',
  render: GiveFeedback
};

export const BuildMyCareerPathPage: Story = {
  name: 'Build My Career Path',
  render: BuildMyCareerPath
};

export const ApprovalInboxPage: Story = {
  name: 'Approval Inbox & Tracking',
  render: ApprovalInbox
};

export const ResumeLerningPage: Story = {
  name: 'Resume Lerning',
  render: ResumeLerning,
  parameters: {
    docs: {
      source: {
        code: `
export const ResumeLerning = () => {
  return (
    <div className="relative inset-0 flex h-full w-full flex-auto gap-24 overflow-y-auto bg-surface-grey_20 py-12 pl-12 pr-12 md:pr-24">
      <div className="hidden h-[876px] w-[92px] max-w-[92px] flex-auto flex-col rounded-xl bg-surface-grey_0 md:flex"></div>

      <div className="flex w-full flex-auto flex-col">
        <div className="flex h-[104px] min-h-[104px] justify-end">
          <div className="h-40 w-[400px] rounded-xl bg-surface-grey_0"></div>
        </div>

        <div className="overflow flex flex-col gap-24 pb-[88px]">
          <div className="flex h-[540px] min-h-[540px] flex-auto justify-end rounded-2xl bg-button-primary-fill-rested">
            <div className="w-[642px] rounded-2xl bg-surface-grey_0"></div>
          </div>

          <Card title="Seaction 1">
            ....
          </Card>
          <Card title="Seaction 2">
            ....
          </Card>
          <Card title="Seaction 3">
            ....
          </Card>

          <Card title="Seaction 4">
            ....
          </Card>

          <ModalFooter className="fixed bottom-0 left-0 right-0 md:bottom-12 md:left-[120px] md:right-36">
            <Link className="px-12">Cancel Learning</Link>
            <Button variant="Primary" size="Medium">
              Resume learning
            </Button>
          </ModalFooter>
        </div>
      </div>
    </div>
  );
};
        `,
        language: 'tsx'
      }
    }
  }
};

export const RatingAuditHistoryPage: Story = {
  name: 'Rating Audit History',
  render: RatingAuditHistory,
  parameters: {
    docs: {
      source: {
        code: `
export const RatingAuditHistory = () => {
  const [isOpen, setIsOpen] = useState(false);
  const id = uniqueId('drawer-');

  const [activeTabId, setActiveTabId] = useState('1');

  const handleTabClick = (id: string) => {
    setActiveTabId(id);
  };

  const items = [
    {
      action: 1,
      date: '13:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Mohammed Al Hashemi',
      position: 'Senior Specialist',
      comment:
        'Please verify skill description, it is to narrow on certain aspects, please make it more generic as mentioned by Khali Al Mansouri. Please verify skill description, it is to narrow on certain aspects, please make it more generic as mentioned by Khali Al Mansouri'
    },
    {
      action: 3,
      date: '10:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Rashi Ali',
      position: 'On behalf of Ahmed Al Blooshi',
      comment:
        'Skill description verified and corrected, please review the new version'
    },
    {
      action: 2,
      date: '13:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Mohammed Al Hashemi',
      position: 'Senior Specialist',
      comment:
        'Please verify skill description, it is to narrow on certain aspects, please make it more generic as mentioned by Khali Al Mansouri'
    },
    {
      action: 4,
      date: '12:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Khalid Al Mansouri',
      position: 'Senior Specialist',
      comment:
        'Please verify skill description, it is to narrow on certain aspects, please make it more generic'
    },
    {
      action: 5,
      date: '11:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Mohammed Al Hashemi',
      position: 'Senior Specialist'
    },
    {
      action: 6,
      date: '10:15 AM, 31 Jan 2024',
      email: '<EMAIL>',
      fullname: 'Rashi Ali',
      position: 'Senior Specialist'
    }
  ];

  return (
    <div className={cn('min-h-[700px] p-4', id)}>
      <Button onClick={() => setIsOpen(true)}>Open Drawer</Button>
      <Drawer
        isOpen={isOpen}
        header="Rating Audit History"
        onBack={() => setIsOpen(false)}
        onClose={() => setIsOpen(false)}
        portalSelector={'.' + id}
        className="p-0"
      >
        <Tabs
          activeTabId={activeTabId}
          size="Large"
          items={[
            {
              id: '1',
              label: 'Self Assessment'
            },
            {
              id: '2',
              label: 'My Team Assessment'
            }
          ]}
          onChange={handleTabClick}
        />

        {activeTabId === '1' && (
          <div className="flex flex-col p-20">
            {items.map((item, index, array) => (
              <ActionHistoryItem
                key={item.action}
                statusMap={ACTION_HISTORY_STATUS_MAP}
                item={item}
                isLastItem={index + 1 === array.length}
              />
            ))}
          </div>
        )}

        {activeTabId === '2' && (
          <div className="flex flex-auto flex-col items-center justify-center">
            <IllustrationMessage
              illustrationVariant={IllustrationVariant.Error}
              title="Sorry, an error has occurred"
              description="We can't find the page you are looking for"
            />
          </div>
        )}
      </Drawer>
    </div>
  );
};
        `,
        language: 'tsx'
      }
    }
  }
};
