import { FC, ReactNode } from 'react';

import { cn } from '@/utils';

import { DropdownTreeListMenuItemContainer } from './DropdownTreeListMenuItemContainer';

import { Avatar } from '../Avatar';
import { Checkbox } from '../Checkbox';
import { DropdownOption, DropdownVariant } from '../Dropdown';
import { TooltipProps } from '../Tooltip';
import { DataAttributesProps } from '../types';

export type DropdownTreeListMenuSubItemProps = DataAttributesProps & {
  title: ReactNode;
  description?: ReactNode;
  option: DropdownOption;
  variant: DropdownVariant;
  selected?: boolean;
  selectedOptions: DropdownOption[];
  disabled?: boolean;
  tooltip?: TooltipProps;
  handleSelect?: (option: DropdownOption) => void;
  'data-id'?: string;
  'data-focused'?: boolean;
};

export const DropdownTreeListMenuSubItem: FC<
  DropdownTreeListMenuSubItemProps
> = ({
  title,
  description,
  variant,
  selectedOptions,
  handleSelect,
  option,
  disabled,
  tooltip,
  'data-id': dataId,
  'data-focused': dataFocused,
  dataAttributes = 'DropdownMultiselectListItem'
}) => {
  const handleSelectHandler = (value: DropdownOption) => {
    handleSelect?.(value);
  };

  return (
    <DropdownTreeListMenuItemContainer
      key={dataId}
      id={dataId}
      data-attributes={dataAttributes}
      variant={variant}
      selected={!!selectedOptions.find((listItem) => listItem.id === option.id)}
      disabled={option.disabled || disabled}
      tooltip={tooltip}
      className={cn(dataFocused && 'bg-dropdown-dropdown-hover')}
      onClick={() => handleSelectHandler(option)}
    >
      <div className="flex w-full items-center justify-between pl-[40px]">
        <div className="flex min-w-0 flex-row items-center gap-8 md:!truncate md:!whitespace-nowrap">
          <Checkbox
            size="Medium"
            checked={
              !!selectedOptions.find((listItem) => listItem.id === option.id)
            }
            disabled={option.disabled || disabled}
            onChange={() => handleSelectHandler(option)}
          />
          {option.avatar && <Avatar {...option.avatar} />}
          {description && (
            <div className="flex min-w-0 flex-auto flex-col gap-4 md:!truncate md:!whitespace-nowrap">
              <span
                className={cn(
                  'md:!truncate',
                  'text-body-2-medium text-text-heading',
                  disabled && 'text-text-secondary'
                )}
              >
                {title}
              </span>
              <span
                className={cn(
                  'md:!truncate',
                  'text-body-4-regular text-text-body',
                  disabled && 'text-text-secondary'
                )}
              >
                {description}
              </span>
            </div>
          )}
          {!description && (
            <span className="min-w-0 text-body-2-regular md:!truncate md:!whitespace-nowrap">
              {title}
            </span>
          )}
        </div>
      </div>
    </DropdownTreeListMenuItemContainer>
  );
};
