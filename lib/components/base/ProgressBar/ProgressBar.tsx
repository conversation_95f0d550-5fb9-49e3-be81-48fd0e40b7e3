import * as React from 'react';

import { cva, VariantProps } from 'class-variance-authority';

import { cn } from '@/utils';

import { DataAttributesProps } from '../types';

export const ProgressBarVariant = {
  Primary: 'Primary',
  Warning: 'Warning'
} as const;

export type ProgressBarVariant =
  (typeof ProgressBarVariant)[keyof typeof ProgressBarVariant];

const sharedStyles = 'min-h-8 rounded-xl';

const ProgressBarVariants = cva(sharedStyles, {
  variants: {
    variant: {
      [ProgressBarVariant.Primary]: cn('bg-button-primary-fill-rested'),
      [ProgressBarVariant.Warning]: cn('bg-main-warning-60')
    }
  },
  defaultVariants: {
    variant: ProgressBarVariant.Primary
  }
});

export type ProgressBarProps = React.ComponentProps<'div'> &
  VariantProps<typeof ProgressBarVariants> &
  DataAttributesProps & {
    indicatorClassName?: string;
    value?: number;
  };

export const ProgressBar: React.FC<ProgressBarProps> = ({
  className,
  variant = ProgressBarVariant.Primary,
  indicatorClassName,
  value = 0,
  dataAttributes = 'ProgressBar',
  ...props
}) => {
  const Comp = 'div';
  const normalizedValue = Math.min(100, Math.max(0, value));

  return (
    <Comp
      data-slot="div"
      data-attributes={dataAttributes}
      className={cn(
        'bg-war w-full bg-surface-grey_10',
        sharedStyles,
        className
      )}
      {...props}
    >
      <div
        className={cn(
          ProgressBarVariants({ variant, className }),
          indicatorClassName
        )}
        style={{ width: `${normalizedValue}%` }}
      />
    </Comp>
  );
};
