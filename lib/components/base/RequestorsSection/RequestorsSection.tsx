import { FC } from 'react';

import { cn } from '@/utils';

import { RequestorItem } from './RequestorItem';
import { Requestor } from './RequestorsSection.types';

import { DataAttributesProps } from '../types';

export interface RequestorsSectionProps extends DataAttributesProps {
  requestedBy?: Requestor;
  requestedFor?: Requestor;
  className?: string;
}

export const RequestorsSection: FC<RequestorsSectionProps> = ({
  requestedBy,
  requestedFor,
  className,
  dataAttributes = 'RequestorsSection'
}) => {
  return (
    <div
      data-attributes={dataAttributes}
      className={cn(
        'flex flex-col gap-20 !rounded-2xl bg-surface-grey_0 px-12 py-16 md:!flex-row',
        className
      )}
    >
      {requestedBy && (
        <RequestorItem
          title="Requested by"
          requestor={requestedBy}
          className="grow basis-1/2"
        />
      )}
      {requestedFor && (
        <RequestorItem
          title="Requested for"
          requestor={requestedFor}
          className="grow basis-1/2"
        />
      )}
    </div>
  );
};
