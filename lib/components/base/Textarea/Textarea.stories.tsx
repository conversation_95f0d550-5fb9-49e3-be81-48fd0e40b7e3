import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import type { Meta, StoryObj } from '@storybook/react';

import { Textarea } from './Textarea';
import { TextareaRHF } from './TextareaRHF';
import { TextareaVariant } from './types';

import { Button, ButtonSize, ButtonVariant } from '../Button';

const TextareaWrapper = (args: React.ComponentProps<typeof Textarea>) => {
  const [value, setValue] = useState(args.value);

  useEffect(() => {
    setValue(args.value);
  }, [args.value]);

  return (
    <Textarea
      {...args}
      value={value}
      onChange={(e) => {
        setValue(e.target.value);
      }}
    />
  );
};

const meta = {
  title: 'Design System/Components/Textarea/Textarea',
  component: TextareaWrapper,
  tags: ['autodocs', 'Forms', 'In Review'],
  parameters: {
    docs: {
      description: {
        component:
          'The text area is a user input component designed to accommodate multiline text. It provides a clean, intuitive interface for users to enter and edit content while ensuring a consistent user experience across varying text lengths.'
      }
    }
  },
  args: {
    value: '',
    placeholder: 'Type here...',
    required: false,
    disabled: false,
    variant: TextareaVariant.Primary,
    label: 'Label',
    labelClassName: '',
    showLabel: true,
    supportingText: 'Supporting Text',
    supportingTextClassName: '',
    maxLength: 300,
    rows: 5,
    maxRows: 10,
    trimBeforeCounting: false,
    hideCounter: false,
    drawerHeaderTitle: '',
    drawerSubmitTitle: 'Continue',
    enhanceWithAI: undefined
  },
  argTypes: {
    variant: {
      control: 'radio',
      options: Object.values(TextareaVariant) as TextareaVariant[]
    },
    disabled: {
      control: 'boolean'
    },
    showLabel: {
      control: 'boolean'
    },
    required: {
      control: 'boolean'
    },
    label: {
      control: 'text'
    },
    placeholder: {
      control: 'text'
    },
    supportingText: {
      control: 'text'
    },
    name: {
      control: 'text'
    },
    value: {
      control: 'text'
    },
    rows: {
      control: { type: 'number', min: 1, max: 20 }
    },
    maxRows: {
      control: { type: 'number', min: 1, max: 20 }
    },
    maxLength: {
      control: { type: 'number', min: 1, max: 1000 }
    },
    trimBeforeCounting: {
      control: 'boolean'
    },
    hideCounter: {
      control: 'boolean'
    },
    labelClassName: {
      control: 'text'
    },
    supportingTextClassName: {
      control: 'text'
    },
    drawerHeaderTitle: {
      control: 'text'
    },
    drawerSubmitTitle: {
      control: 'text'
    },
    enhanceWithAI: {
      control: 'text'
    },
    onChange: {
      action: 'changed'
    }
  }
} satisfies Meta<typeof Textarea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  name: 'Variant: Primary',
  parameters: {
    docs: {
      description: {
        story: 'Another description on the story, overriding the comments'
      }
    }
  }
};

export const Negative: Story = {
  name: 'Variant: Error',
  args: {
    variant: TextareaVariant.Error,
    supportingText: 'Error message'
  }
};

export const Disabled: Story = {
  name: 'State: Disabled',
  args: {
    disabled: true
  }
};

export const Required: Story = {
  name: 'State: Required',
  args: {
    required: true
  }
};

export const WithCounter: Story = {
  name: 'Features: With Counter',
  args: {
    value: 'This is a sample text',
    maxLength: 100
  }
};

export const WithoutCounter: Story = {
  name: 'Features: Without Counter',
  args: {
    value: 'This is a sample text',
    hideCounter: true
  }
};

export const WithoutLabel: Story = {
  name: 'Features: Without Label',
  args: {
    showLabel: false
  }
};

export const EnhanceWithAI: Story = {
  name: 'Enhance with AI',
  args: {
    enhanceWithAI: (
      <Button
        size={ButtonSize.ExtraSmall}
        variant={ButtonVariant.SecondaryPurple}
        leftIcon="AI_fill"
        onClick={() => {
          alert('Enhance with AI');
        }}
      >
        Enhance with AI
      </Button>
    )
  }
};

const ReactHookFormIntegrationTemplate = () => {
  const methods = useForm({
    mode: 'onChange',
    defaultValues: {
      description: '',
      comments: 'it is my first comment'
    }
  });

  const onSubmit = (data: unknown) => {
    alert(JSON.stringify(data));
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="flex w-full flex-col"
      >
        <div className="flex w-full flex-col items-start gap-4 pb-12">
          <TextareaRHF name="description" label="Description" />
          <TextareaRHF name="comments" label="Comments" />
        </div>
        <Button type="submit" className="py-2 rounded">
          Submit
        </Button>
      </form>
    </FormProvider>
  );
};

export const FormIntegration: Story = {
  name: 'React Hook Form',
  render: ReactHookFormIntegrationTemplate,
  parameters: {
    docs: {
      source: {
        code: `
const ReactHookFormTemplate = () => {
  const methods = useForm({
    mode: 'onChange',
    defaultValues: {
      improve1: false,
      improve2: false,
      improve3: false
    }
  });

  const onSubmit = (data: unknown) => {
    alert(JSON.stringify(data));
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <h3>How can we improve?</h3>
        <div className="flex flex-col items-start gap-4 pb-12">
          <Checkbox
            name="improve1"
            value={111}
            label="Improve ease of use of the application"
            size={CheckboxSize.Small}
          />
          <Checkbox
            name="improve2"
            value={222}
            label="Enhance guidelines / process"
            size={CheckboxSize.Small}
          />
          <Checkbox
            name="improve3"
            value={333}
            label="Provide better HC support"
            size={CheckboxSize.Small}
          />
        </div>
        <Button type="submit" className="py-2 rounded">
          Submit
        </Button>
      </form>
    </FormProvider>
  );
};
        `,
        language: 'tsx'
      }
    }
  }
};
