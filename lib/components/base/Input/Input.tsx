import { forwardRef, ReactNode, useEffect, useRef, useState } from 'react';

import { cva, VariantProps } from 'class-variance-authority';

import { useIsMobile } from '@/hooks';
import { cn, generateId } from '@/utils';

import { Icon, IconName } from '../Icon';
import { DataAttributesProps } from '../types';

export const InputVariant = {
  Primary: 'Primary',
  Positive: 'Positive',
  Negative: 'Negative', // @deprecated
  Error: 'Error'
} as const;

export type InputVariant =
  | 'Primary'
  | 'Positive'
  /**
   * @deprecated Use 'Error' instead
   */
  | 'Negative'
  | 'Error';

export const InputSize = {
  Large: 'Large',
  Medium: 'Medium',
  Small: 'Small'
} as const;

export type InputSize = (typeof InputSize)[keyof typeof InputSize];

const inputVariants = cva(
  'flex flex-auto items-center justify-center rounded-lg border border-solid focus-within:ring-0 text-input-text-placeholder placeholder:placeholder-input-text-placeholder w-full',
  {
    variants: {
      variant: {
        [InputVariant.Primary]: cn(
          'border-transparent bg-input-fill-placeholder',
          'hover:border-input-stroke-hover',
          'focus-within:border-input-stroke-pressed focus-within:ring-input-stroke-pressed focus-within:bg-input-fill-active focus-within:text-input-text-filled',
          'focus-within:hover:border-input-stroke-pressed focus-within:hover:ring-input-stroke-pressed'
        ),
        [InputVariant.Negative]: cn(
          'border-input-stroke-error-rested bg-input-fill-active',
          'hover:border-input-stroke-error-hover',
          'focus-within:border-input-stroke-error-pressed focus-within:ring-input-stroke-error-pressed'
        ),
        [InputVariant.Error]: cn(
          'border-input-stroke-error-rested bg-input-fill-active',
          'hover:border-input-stroke-error-hover',
          'focus-within:border-input-stroke-error-pressed focus-within:ring-input-stroke-error-pressed'
        ),
        [InputVariant.Positive]: cn(
          'border-input-stroke-success-rested bg-input-fill-active ',
          'hover:border-input-stroke-success-hover',
          'focus-within:border-input-stroke-success-pressed focus-within:ring-input-stroke-success-pressed'
        )
      },
      size: {
        [InputSize.Large]: 'h-48 px-12 text-body-1-regular',
        [InputSize.Medium]: 'h-40 px-8 text-body-2-regular',
        [InputSize.Small]: 'h-32 px-8 text-body-2-regular'
      }
    },
    defaultVariants: {
      variant: InputVariant.Primary,
      size: InputSize.Large
    }
  }
);

const inputContentVariants = cva(
  'flex flex-1 bg-transparent text-input-text-filled outline-none placeholder:text-input-text-placeholder focus:outline-none disabled:placeholder:text-input-text-disabled disabled:cursor-not-allowed disabled:border-input-stroke-disabled',
  {
    variants: {
      size: {
        [InputSize.Large]: cn('h-48 px-8'),
        [InputSize.Medium]: cn('h-40 px-4'),
        [InputSize.Small]: cn('h-32 px-4')
      }
    },
    defaultVariants: {
      size: InputSize.Large
    }
  }
);

const supportingTextVariants = cva('text-label-xs-regular mt-[2px] ml-12', {
  variants: {
    variant: {
      [InputVariant.Primary]: cn('text-text-heading'),
      [InputVariant.Negative]: cn('text-input-text-error'),
      [InputVariant.Error]: cn('text-input-text-error'),
      [InputVariant.Positive]: cn('text-input-text-success')
    }
  },
  defaultVariants: {
    variant: InputVariant.Primary
  }
});

const labelVariants = cva('text-text-heading mb-4', {
  variants: {
    size: {
      [InputSize.Large]: cn('text-label-m1-regular'),
      [InputSize.Medium]: cn('text-label-s-regular'),
      [InputSize.Small]: cn('text-label-s-regular')
    }
  },
  defaultVariants: {
    size: InputSize.Large
  }
});

const iconVariants = cva('', {
  variants: {
    size: {
      [InputSize.Large]: 'h-24 max-h-24 min-h-24 w-24 min-w-24 max-w-24',
      [InputSize.Medium]: 'h-20 max-h-20 min-h-20 w-20 min-w-20 max-w-20',
      [InputSize.Small]: 'h-20 max-h-20 min-h-20 w-20 min-w-20 max-w-20'
    }
  },
  defaultVariants: {
    size: InputSize.Medium
  }
});

export type InputProps = Omit<React.ComponentProps<'input'>, 'size'> &
  VariantProps<typeof inputVariants> &
  DataAttributesProps & {
    size?: InputSize | null | undefined;
    label?: React.ReactNode;
    showLabel?: boolean;
    labelClassName?: string;
    containerClassName?: string;
    inputClassName?: string;
    inputContentClassName?: string;
    leftIcon?: IconName;
    rightIcon?: IconName;
    showClear?: boolean;
    supportingText?: React.ReactNode;
    supportingTextClassname?: string;
    badges?: ReactNode;
    onClear?: () => void;
    onLeftIconClick?: () => void;
    onRightIconClick?: () => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  };

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      id,
      name,
      label = 'Label',
      showLabel = true,
      labelClassName,
      containerClassName,
      inputClassName,
      inputContentClassName,
      leftIcon,
      rightIcon,
      variant = InputVariant.Primary,
      value,
      size,
      placeholder = 'Type here...',
      disabled,
      readOnly,
      required,
      showClear = true,
      supportingText,
      supportingTextClassname,
      badges,
      onChange,
      onClear,
      onLeftIconClick,
      onRightIconClick,
      onKeyDown,
      onFocus,
      dataAttributes = 'Input',
      maxLength,
      ...props
    },
    forwardedRef
  ) => {
    const inputId = id || generateId(name || 'input');
    const divRef = useRef<HTMLDivElement>(null);
    const isMobile = useIsMobile();

    const [inputValue, setInputValue] = useState<
      string | number | readonly string[] | undefined
    >(value || '');

    useEffect(() => {
      setInputValue(value);
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Limit max length inside onChange handler is required for Android OS
      setInputValue(e.target.value.slice(0, maxLength));
      onChange?.({
        ...e,
        target: {
          ...e.target,
          value: e.target.value.slice(0, maxLength)
        }
      });
    };

    const handleClear = () => {
      setInputValue('');
      onClear?.();
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement, Element>) => {
      if (isMobile) {
        setTimeout(() => {
          divRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }, 300);
      }

      onFocus?.(e);
    };

    return (
      <div
        ref={divRef}
        className={cn(
          'group relative flex w-full flex-col gap-4',
          {
            filled: !!inputValue,
            empty: !inputValue
          },
          containerClassName
        )}
        data-attributes={dataAttributes}
      >
        {/* Label */}
        {label && showLabel && (
          <label
            htmlFor={inputId}
            className={cn(
              labelVariants({
                size,
                className: labelClassName
              })
            )}
          >
            {label}
            {required && <span className="ml-4">*</span>}
          </label>
        )}

        {/* Input Container */}
        <div
          className={cn(
            inputVariants({
              variant,
              size,
              className: inputClassName
            }),
            // TODO: Fix it later
            !!inputValue &&
              variant === InputVariant.Primary &&
              '!border-input-stroke-defualt !bg-input-fill-active text-input-text-filled',
            !!inputValue &&
              variant === InputVariant.Positive &&
              'border-input-stroke-success-rested bg-input-fill-active text-input-text-filled',
            !!inputValue &&
              variant === InputVariant.Negative &&
              'border-input-stroke-error-rested bg-input-fill-active text-input-text-filled',
            !!inputValue &&
              variant === InputVariant.Error &&
              'border-input-stroke-error-rested bg-input-fill-active text-input-text-filled'
          )}
        >
          {/* Left Icon */}
          {leftIcon && (
            <Icon
              name={leftIcon}
              className={iconVariants({ size })}
              onClick={onLeftIconClick}
            />
          )}

          {/* Badges */}
          {badges}

          {/* Input */}
          <input
            ref={forwardedRef}
            type="text"
            id={inputId}
            className={cn(
              inputContentVariants({ size, className: inputContentClassName })
            )}
            value={inputValue}
            placeholder={placeholder}
            onChange={handleChange}
            onKeyDown={onKeyDown}
            onFocus={handleFocus}
            disabled={disabled}
            readOnly={readOnly}
            maxLength={maxLength}
            {...props}
          />

          {/* Clear Icon */}
          {inputValue && !disabled && showClear && (
            <Icon
              name="Close_round_light"
              className={cn(
                iconVariants({ size }),
                'mr-4 cursor-pointer',
                disabled && 'cursor-not-allowed'
              )}
              onClick={handleClear}
            />
          )}

          {/* Right Icon */}
          {rightIcon && (
            <Icon
              name={rightIcon}
              className={cn(
                iconVariants({ size }),
                'cursor-pointer',
                disabled && 'cursor-not-allowed'
              )}
              onClick={onRightIconClick}
            />
          )}
        </div>

        {supportingText && (
          <span
            className={cn(
              supportingTextVariants({
                variant,
                className: supportingTextClassname
              })
            )}
          >
            {supportingText}
          </span>
        )}
      </div>
    );
  }
);
