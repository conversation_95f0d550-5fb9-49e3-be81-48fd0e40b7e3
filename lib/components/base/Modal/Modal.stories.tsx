import { useEffect, useState } from 'react';

import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Modal, ModalVariant } from './Modal';

import { Button, ButtonSize, ButtonVariant } from '../Button';
import { DatePicker } from '../DatePicker';
import { DropdownOption } from '../Dropdown';
import { DropdownSingleSelect } from '../DropdownSingleSelect';
import { Loader, LoaderNextSize } from '../Loader';

const meta: Meta<typeof Modal> = {
  title: 'Design System/Components/Modal/Modal',
  tags: ['autodocs', 'Stable'],
  component: Modal,
  parameters: {
    docs: {
      description: {
        component:
          'A modal component that displays content in a dialog box over the main content.'
      }
    }
  },
  args: {
    isOpen: false,
    variant: ModalVariant.Primary,
    header: 'Header',
    dataAttributes: 'Modal',
    showBack: false,
    showClose: false
  },
  argTypes: {
    isOpen: {
      description: 'Controls whether the modal is visible',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    variant: {
      description: 'The visual style variant of the modal',
      control: 'select',
      options: Object.values(ModalVariant),
      table: {
        type: { summary: 'ModalVariant' },
        defaultValue: { summary: ModalVariant.Primary }
      }
    },
    header: {
      description: 'The header content of the modal',
      control: 'text',
      table: {
        type: { summary: 'ReactNode' }
      }
    },
    children: {
      description: 'The main content of the modal',
      control: 'text',
      table: {
        type: { summary: 'ReactNode' }
      }
    },
    footer: {
      description: 'The footer content of the modal',
      control: 'text',
      table: {
        type: { summary: 'ReactNode' }
      }
    },
    showBack: {
      description: 'Whether to show the back button',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    showClose: {
      description: 'Whether to show the close button',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' }
      }
    },
    dataAttributes: {
      description: 'Data attributes for testing and identification',
      control: 'text',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'Modal' }
      }
    },
    onBack: {
      action: 'onBack',
      description: 'Callback when back button is clicked'
    },
    onClose: {
      action: 'onClose',
      description: 'Callback when close button is clicked'
    }
  }
};

export default meta;
type Story = StoryObj<typeof Modal>;

const Template = (args: Parameters<typeof Modal>[0]) => {
  const [isOpen, setIsOpen] = useState(args.isOpen || false);

  useEffect(() => {
    setIsOpen(args.isOpen || false);
  }, [args.isOpen]);

  return (
    <div className="flex w-full items-center justify-center p-4">
      <Button onClick={() => setIsOpen(true)}>Open Modal</Button>
      <Modal
        {...args}
        isOpen={isOpen}
        header={args.header}
        onClose={() => setIsOpen(false)}
        onBack={() => setIsOpen(false)}
        footer={
          <>
            <Button
              variant={ButtonVariant.Secondary}
              size={ButtonSize.Medium}
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant={ButtonVariant.Primary}
              size={ButtonSize.Medium}
              onClick={() => setIsOpen(false)}
            >
              Save
            </Button>
          </>
        }
      />
    </div>
  );
};

export const Default: Story = {
  render: Template
};

export const Primary: Story = {
  name: 'Variant: Primary',
  render: Template,
  args: {
    variant: ModalVariant.Primary
  }
};

export const Secondary: Story = {
  name: 'Variant: Secondary',
  render: Template,
  args: {
    variant: ModalVariant.Secondary
  }
};

export const WithCloseButton: Story = {
  name: 'Modal With Close Button',
  render: Template,
  args: {
    showClose: true
  }
};

export const WithoutCloseButton: Story = {
  name: 'Modal Without Close Button',
  render: Template,
  args: {
    showClose: false
  }
};

export const WithoutBackButton: Story = {
  name: 'Modal With Back Button',
  render: Template,
  args: {
    showClose: false,
    showBack: true
  }
};

export const LongHeader: Story = {
  name: 'Modal With Long Header',
  render: Template,
  args: {
    header:
      'What is Lorem Ipsum? Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys standard dummy text ever since the 1500s'
  }
};

export const LongText: Story = {
  name: 'Modal With Long Content',
  render: Template,
  args: {
    header: 'Header',
    children: `What is Lorem Ipsum?
Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.

Why do we use it?
It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).


Where does it come from?
Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32.

The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.

Where can I get some?
There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.`
  }
};

export const WithLoader: Story = {
  name: 'Modal With Loader',
  render: Template,
  args: {
    header: 'Header',
    children: <Loader size={LoaderNextSize.Small} />
  }
};

const abc = [
  'a',
  'b',
  'c',
  'd',
  'e',
  'f',
  'g',
  'h',
  'i',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'p',
  'r',
  's',
  't',
  'u',
  'w',
  'x',
  'y',
  'z'
];

const OPTIONS: DropdownOption[] = abc.map((i, index) => {
  return {
    id: index,
    title: `Option ${i.toUpperCase()}`,
    disabled: index === 3,
    tooltip:
      index === 3
        ? {
            title: 'This option is disabled because of ...'
          }
        : undefined
  };
});

const Dropdown = () => {
  const [value, setValue] = useState<DropdownOption | undefined>();

  return (
    <DropdownSingleSelect
      onChange={(option) => setValue(option)}
      options={OPTIONS}
      value={value}
    />
  );
};

export const WithDropdown: Story = {
  name: 'Modal With Dropdown',
  render: Template,
  args: {
    header: 'Header',
    children: <Dropdown />
  }
};

export const WithDatePicker: Story = {
  name: 'Modal With DatePicker',
  render: Template,
  args: {
    header: 'Header',
    children: <DatePicker />
  }
};
