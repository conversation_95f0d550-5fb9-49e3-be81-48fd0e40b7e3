import { FieldValues } from 'react-hook-form';

import {
  DropdownMultiselect,
  DropdownMultiselectProps
} from './DropdownMultiselect';

import { HookFormConnector } from '../HookForm/HookFormConnector';
import { HookFormFieldCommonProps } from '../HookForm/types';

export type DropdownMultiselectRHFProps<TFieldValues extends FieldValues> =
  DropdownMultiselectProps & HookFormFieldCommonProps<TFieldValues>;

/** DropdownMultiselect with React Hook Form validation */
export function DropdownMultiselectRHF<TFieldValues extends FieldValues>({
  name,
  control,
  ...inputProps
}: DropdownMultiselectRHFProps<TFieldValues>) {
  return (
    <HookFormConnector
      name={name}
      control={control}
      renderField={({ variant, ...fieldProps }) => (
        <DropdownMultiselect
          {...inputProps}
          {...fieldProps}
          inputVariant={variant}
        />
      )}
    />
  );
}
