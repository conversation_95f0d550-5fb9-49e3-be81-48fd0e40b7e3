import { FC, ReactNode, useState } from 'react';

import { cn } from '@/utils';

import { Avatar, AvatarSize } from '../Avatar';
import { TooltipMode, TruncatedText } from '../Text';
import { Tooltip, TooltipProps, TooltipSize } from '../Tooltip';
import { DataAttributesProps } from '../types';

export const TagAlignment = {
  Top: 'top',
  Center: 'center'
} as const;

export type TagAlignment = (typeof TagAlignment)[keyof typeof TagAlignment];

type TableUserInfoCellProps = React.ComponentProps<'span'> &
  DataAttributesProps & {
    email: string;
    fullName: string;
    position?: string;
    company?: string;
    size?: AvatarSize;
    className?: string;
    tags?: ReactNode;
    tagAlignment?: TagAlignment;
    tooltipMode?: TooltipMode;
  };

export const TableUserInfoCell: FC<TableUserInfoCellProps> = ({
  email,
  fullName,
  position,
  company,
  size = AvatarSize.Medium,
  className,
  tags,
  tagAlignment = TagAlignment.Top,
  dataAttributes = 'TableUserInfo',
  tooltipMode = 'truncated'
}) => {
  const [isTitleTruncated, setIsTitleTruncated] = useState(false);
  const [isDescriptionTruncated, setIsDescriptionTruncated] = useState(false);

  const title = fullName;
  const description = [position, company].filter((item) => !!item).join(', ');

  const tooltip: TooltipProps = {
    heading: title,
    title: description,
    size: TooltipSize.Large,
    enableForMobile: false
  };

  const renderUserInfo = () => {
    return (
      <>
        {tagAlignment === TagAlignment.Top && (
          <div className="flex items-center">{tags}</div>
        )}
        <div className="flex flex-row items-center gap-4">
          <TruncatedText
            key={title}
            lineClamp={size === AvatarSize.Small ? 2 : description ? 1 : 2}
            className={cn(
              size === AvatarSize.Small
                ? 'text-input-text-filled'
                : 'text-text-heading',
              size === AvatarSize.Small
                ? 'text-body-2-regular'
                : 'text-body-1-medium'
            )}
            onTruncationStateChange={setIsTitleTruncated}
          >
            {title}
          </TruncatedText>
          {tagAlignment === TagAlignment.Center && tags}
        </div>
        {description && size !== AvatarSize.Small && (
          <TruncatedText
            key={description}
            lineClamp={1}
            className="text-body-2-regular text-text-body"
            onTruncationStateChange={setIsDescriptionTruncated}
          >
            {description}
          </TruncatedText>
        )}
      </>
    );
  };

  const renderUserInfoWithTooltip = () => {
    return <Tooltip {...tooltip}>{renderUserInfo()}</Tooltip>;
  };
  return (
    <div
      data-attributes={dataAttributes}
      className={cn(
        'flex flex-row items-center gap-12',
        size === AvatarSize.Small ? 'px-12' : 'px-0',
        className
      )}
    >
      <Avatar email={email} fullName={fullName} size={size} />
      <div className="flex flex-col">
        {tooltipMode === 'always' || isTitleTruncated || isDescriptionTruncated
          ? renderUserInfoWithTooltip()
          : renderUserInfo()}
      </div>
    </div>
  );
};
