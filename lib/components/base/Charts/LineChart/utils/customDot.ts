interface CustomDotConfig {
  color: string;
  size?: number;
  strokeWidth?: number;
}

export function createCustomDot({
  color,
  size = 6,
  strokeWidth = 2
}: CustomDotConfig) {
  return {
    r: size,
    fill: color,
    stroke: color,
    strokeWidth,
    fillOpacity: 1
  };
}

export function createCustomActiveDot({
  color,
  size = 8,
  strokeWidth = 3
}: CustomDotConfig) {
  return {
    r: size,
    fill: color,
    stroke: color,
    strokeWidth,
    fillOpacity: 1
  };
}
