import { FC, PropsWithChildren, ReactNode, useRef, useState } from 'react';

import {
  arrow,
  autoUpdate,
  flip,
  FloatingArrow,
  FloatingPortal,
  offset,
  Placement,
  safePolygon,
  shift,
  Strategy,
  useFloating,
  useHover,
  useInteractions
} from '@floating-ui/react';
import { cva } from 'class-variance-authority';
import clsx from 'clsx';

import { useIsMobile } from '@/hooks';
import { cn, getPortalElement } from '@/utils';

// Import React-style global state management for tooltip drawer interactions
// This prevents dropdown contexts from closing when tooltip drawers are open
import { setTooltipDrawerOpen } from './TooltipDrawerGlobalState';

import { Drawer } from '../Drawer';
import { useUIKitModalsSelectors } from '../UIKitProvider';

const ARROW_HEIGHT = 6;
const GAP = 1;

export const TooltipVariant = {
  Dark: 'Dark',
  Light: 'Light'
} as const;

export type TooltipVariant =
  (typeof TooltipVariant)[keyof typeof TooltipVariant];

export const TooltipSize = {
  Large: 'Large',
  Medium: 'Medium',
  Small: 'Small'
} as const;

export type TooltipSize = (typeof TooltipSize)[keyof typeof TooltipSize];

export type TooltipProps = {
  heading?: ReactNode;
  title?: ReactNode;
  variant?: TooltipVariant;
  size?: TooltipSize;
  placement?: Placement;
  strategy?: Strategy;
  className?: string;
  classNameTrigger?: string;
  fallbackPlacements?: Placement[];
  enableForMobile?: boolean;
};

const variant = {
  [TooltipVariant.Dark]: 'bg-tooltip-fill_default text-tooltip-text',
  [TooltipVariant.Light]: 'bg-surface-grey_0 text-text-body'
};
const size = {
  [TooltipSize.Large]: 'max-w-[230px]',
  [TooltipSize.Medium]: 'max-w-[170px]',
  [TooltipSize.Small]: 'max-w-[120px]'
};

export const tooltipVariants = cva(
  'shadow-tooltip z-[1080] px-8 py-4 text-body-4-regular rounded-md [overflow-wrap:anywhere]',
  {
    variants: { variant, size },
    defaultVariants: {
      variant: TooltipVariant.Dark,
      size: TooltipSize.Large
    }
  }
);

export const tooltipVariantsWithHeading = cva(
  'shadow-tooltip z-[1080] p-12 text-body-3-regular rounded-lg [overflow-wrap:anywhere]',
  {
    variants: { variant, size },
    defaultVariants: {
      variant: TooltipVariant.Dark,
      size: TooltipSize.Large
    }
  }
);

export const Tooltip: FC<PropsWithChildren<TooltipProps>> = ({
  children,
  heading,
  title,
  variant = TooltipVariant.Dark,
  size = TooltipSize.Large,
  placement = 'top',
  strategy = 'fixed',
  fallbackPlacements = ['top', 'bottom'],
  className,
  classNameTrigger,
  enableForMobile = true
}) => {
  const arrowRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const isMobile = useIsMobile();
  const { portalClassName } = useUIKitModalsSelectors();

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => {
      setIsOpen(open);
    },
    strategy,
    placement,
    middleware: [
      shift(),
      arrow({
        element: arrowRef
      }),
      offset(ARROW_HEIGHT + GAP),
      flip({ fallbackPlacements })
    ],
    whileElementsMounted: autoUpdate
  });

  const hover = useHover(context, {
    enabled: !isDrawerOpen && !isMobile,
    restMs: 50,
    delay: 100,
    handleClose: safePolygon()
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([hover]);

  const containerClassName = heading
    ? tooltipVariantsWithHeading({ variant, size, className })
    : tooltipVariants({ variant, size, className });

  const handleClick = (e: React.MouseEvent | React.TouchEvent) => {
    if (isMobile && enableForMobile) {
      // Prevent dropdown item click when clicking tooltip trigger
      e.stopPropagation();
      e.preventDefault();
      setIsDrawerOpen(true);
      setTooltipDrawerOpen(true);
    }
  };

  const handleTouchStart = (e: React.MouseEvent | React.TouchEvent) => {
    if (isMobile && enableForMobile) {
      e.stopPropagation();
      e.preventDefault();
      setIsDrawerOpen(true);
      setTooltipDrawerOpen(true);
    }
  };

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
    setTooltipDrawerOpen(false);
  };

  if (!heading && !title) {
    return children;
  }

  return (
    <>
      <div
        className={cn(
          'tooltip-trigger',
          isMobile && enableForMobile && 'cursor-pointer',
          classNameTrigger
        )}
        ref={refs.setReference}
        {...getReferenceProps()}
        onClick={handleClick}
        onTouchStart={handleTouchStart}
      >
        {children}
      </div>
      {isOpen && !isMobile && !isDrawerOpen && (heading || title) && (
        <FloatingPortal root={getPortalElement(portalClassName)}>
          <div
            className={cn(containerClassName)}
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
          >
            <FloatingArrow
              className={clsx({
                'fill-tooltip-fill_default': variant === TooltipVariant.Dark,
                'fill-surface-grey_0': variant === TooltipVariant.Light
              })}
              tipRadius={2}
              height={6}
              width={8}
              ref={arrowRef}
              context={context}
            />
            <div className="flex max-h-[350px] flex-col gap-8 overflow-y-auto">
              {heading && <h3 className="text-cta-2-medium">{heading}</h3>}
              {title}
            </div>
          </div>
        </FloatingPortal>
      )}

      <Drawer
        isOpen={isDrawerOpen && !!(heading || title) && isMobile}
        onClose={handleCloseDrawer}
        fullHeight={false}
        footer={undefined}
      >
        {heading && (
          <h3 className="mb-8 text-cta-2-medium text-text-heading">
            {heading}
          </h3>
        )}
        {title && (
          <span className="text-body-2-regular text-text-heading">{title}</span>
        )}
      </Drawer>
    </>
  );
};
