import { ReactNode, useMemo } from 'react';

import { TooltipProps } from '../../Tooltip';

export type TooltipMode = 'always' | 'truncated' | 'off';

type UseTooltipProps = {
  tooltip?: TooltipProps | string;
  tooltipMode?: TooltipMode;
  isTruncated?: boolean;
  children?: ReactNode;
};

/** Helper which allows to collect tooltip props properly */
export const useTooltipProps = ({
  tooltip,
  tooltipMode = 'always',
  isTruncated,
  children
}: UseTooltipProps) => {
  const isFullTooltipProps = typeof tooltip === 'object';

  return useMemo(() => {
    if (tooltipMode === 'off') {
      return;
    }

    if (tooltipMode === 'always') {
      const tooltipTitle =
        (isFullTooltipProps ? tooltip.title : tooltip) ?? children;

      return {
        ...(isFullTooltipProps
          ? { ...tooltip, title: tooltipTitle }
          : { title: tooltipTitle })
      };
    }
    if (tooltipMode === 'truncated') {
      const tooltipTitle = isTruncated
        ? ((isFullTooltipProps ? tooltip.title : tooltip) ?? children)
        : undefined;
      return {
        ...(isFullTooltipProps
          ? { ...tooltip, title: tooltipTitle }
          : { title: tooltipTitle })
      };
    }

    return;
  }, [children, isFullTooltipProps, isTruncated, tooltip, tooltipMode]);
};
