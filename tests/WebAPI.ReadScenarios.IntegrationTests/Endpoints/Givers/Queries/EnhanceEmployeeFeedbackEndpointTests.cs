using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Givers.Queries;

public class EnhanceEmployeeFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetEnhancedFeedback_SuccessFlow_ReturnsEnhancedFeedbackAsync()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                ReceiverId = "99905179",
                RequestorId = "99905179",
                ObjectiveId = "99905179",
                TaskAndMilestoneId = "99905179",
                TopicId = FeedbackTopics.General,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.EnhanceWriting,
                Prompt = "Provide detailed feedback on the topic."
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetGeneratedFeedback_SuccessFlow_ReturnsEnhancedFeedbackAsync()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                ReceiverId = "99905179",
                RequestorId = "99905179",
                ObjectiveId = "99905179",
                TaskAndMilestoneId = "99905179",
                TopicId = FeedbackTopics.General,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.Generate
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedback_WhenFieldsAreInvalid_ReturnsBadRequestWithValidationDetailAsync()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                TopicId = FeedbackTopics.General,
                Subject = "",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.CustomPrompt,
                Prompt = new string('x', 4001)
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .IgnoreMember("traceId")
            .IgnoreMember("exceptionType");
    }

    [Fact]
    public async Task GetEnhancedFeedbackWithObjective_SuccessFlow_ReturnsEnhancedFeedbackAsync()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                ReceiverId = "99905179",
                ObjectiveId = "43750282300060001",
                TopicId = FeedbackTopics.Objective,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.EnhanceWriting,
                Prompt = "Provide detailed feedback on the topic."
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedbackWithTask_SuccessFlow_ReturnsEnhancedFeedbackAsync()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                ReceiverId = "99905179",
                TaskAndMilestoneId = "33750282300060001",
                TopicId = FeedbackTopics.TaskAndMilestone,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.EnhanceWriting,
                Prompt = "Provide detailed feedback on the topic."
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedbackWithMilestone_SuccessFlow_ReturnsEnhancedFeedbackAsync()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                ReceiverId = "99905179",
                TaskAndMilestoneId = "33750282300060006",
                TopicId = FeedbackTopics.TaskAndMilestone,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.EnhanceWriting,
                Prompt = "Provide detailed feedback on the topic."
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedback_RequestorIdNull_ReturnsSuccess()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                RequestorId = (string)null,
                ReceiverIds = new string[] { "99905179" },
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedback_ReceiverIdsEmpty_ReturnsValidationError()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                RequestorId = "99905179",
                ReceiverIds = Array.Empty<string>(),
                TopicId = FeedbackTopics.General,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.EnhanceWriting,
                Prompt = "Provide detailed feedback on the topic."
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .IgnoreMember("traceId")
            .IgnoreMember("exceptionType");
        ;
    }

    [Fact]
    public async Task GetEnhancedFeedback_ReceiverDifferentFromRequestor_Success()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                RequestorId = "99905179",
                ReceiverIds = new string[] { "99905180" },
                TopicId = FeedbackTopics.General,
                Subject = "Performance Review",
                Rating = 5,
                RequestDetails = "Detailed explanation of the request.",
                FeedbackText = "This is a sample feedback text.",
                Action = EnhanceActionType.EnhanceWriting,
                Prompt = "Provide detailed feedback on the topic."
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedback_OnlyWithAction_Success()
    {
        SetAuthToken(Auth.IMartinezToken);

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                ReceiverIds = Array.Empty<string>(),
                Action = EnhanceActionType.MakeLonger,
            }).ToUrl("/v1/givers/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }
}
