{
  items: [
    {
      id: 65490283500050007,
      employee: {
        employeeId: 99905182,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON><PERSON>,
        email: a<PERSON><PERSON><PERSON>@adnoc.ae,
        jobTitle: Product Manager,
        positionName: Manager, Product Development
      },
      date: 2026-04-10T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Requesting Feedback on Last Week's Performance
      },
      status: {
        id: completed,
        name: Completed
      },
      rating: 4,
      requestDetails: Can you share feedback on my performance during the last week?,
      feedbackText: Your delivery was confident, and you highlighted key points that resonated with the client. Great job maintaining engagement!
    },
    {
      id: 65490283500050025,
      employee: {
        employeeId: 99905180,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Financial Analyst,
        positionName: Analyst, Financial Planning
      },
      date: 2025-04-10T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Feedback request
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: I’d love to hear your feedback on how my subordinate handled pricing negotiations during the recent deal.,
      feedbackText: He handled the pricing discussions with confidence and agility, demonstrating strong negotiation skills.
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 2,
    count: 2,
    totalResults: 5
  }
}