using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Managers.Queries;

public class GetSubordinateReceivedFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetSubordinateReceivedFeedback_SuccessFlow_ReturnsFeedbackAsync()
    {
        SetAuthToken(Auth.ManagerToken);

        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905178/feedback/65490283500050004");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await Verify<PERSON>son(actualObject)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task GetSubordinateReceivedFeedback_NonExistedId_ReturnsNotFoundAsync()
    {
        SetAuthToken(Auth.ManagerToken);

        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905178/feedback/11100111100010011");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetSubordinateReceivedFeedback_SubordinateIsNotReceiver_ReturnsNotFoundAsync()
    {
        SetAuthToken(Auth.ManagerToken);

        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905178/feedback/65490283500050014");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetSubordinateReceivedFeedback_CurrentUserIsNotSubordinateManager_ReturnsForbiddenAsync()
    {
        SetAuthToken(Auth.EBrownToken);

        await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905178/feedback/65490283500050004");
            api.StatusCodeShouldBe(HttpStatusCode.Forbidden);
        });
    }

    [Fact]
    public async Task GetSubordinateReceivedFeedback_TaskAndMilestone_ReturnsFeedbackAsync()
    {
        SetAuthToken(Auth.ManagerToken);

        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905178/feedback/65490283500050016");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task GetSubordinateReceivedFeedback_HiddenForReceiver_ReturnsFeedbackAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905181/feedback/65490283500050026");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task GetSubordinateReceivedFeedback_IgnoreManagerVisibilityWhenTemplateDisallows_ReturnsAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/managers/me/receivers/99905179/feedback/65490283500050028");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .DontScrubDateTimes();
    }
}
