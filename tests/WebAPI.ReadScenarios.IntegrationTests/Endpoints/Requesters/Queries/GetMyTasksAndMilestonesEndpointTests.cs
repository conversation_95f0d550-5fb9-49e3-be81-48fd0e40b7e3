using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class GetMyTasksAndMilestonesEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyTasksAndMilestones_SuccessFlow_TaskAndMilestonesAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/tasks-milestones");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
