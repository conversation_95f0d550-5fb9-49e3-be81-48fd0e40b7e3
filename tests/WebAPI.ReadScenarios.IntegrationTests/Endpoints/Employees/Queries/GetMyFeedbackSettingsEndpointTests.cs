using Microsoft.AspNetCore.Http;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Employees.Queries;

public class GetMyFeedbackSettingsEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyFeedbackSettings_Manager_ReturnsFeedbackSettings()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/feedback-settings");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetMyFeedbackOverview_NotEmployee_ReturnsFeedbackOverviewAsync()
    {
        // Arrange
        SetAuthToken(Auth.NotEmployeeToken);

        var response = await Host.Scenario(api =>
        {
            // Act
            api.Get.Url("/v1/employees/me/feedback-settings");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }
}
