@startuml component-diagram

package "FeedbackService.WebAPI" {
    [WebAPI] as WebAPI
}

package "FeedbackService.Application" {
    [FeedbackQueryService] as FeedbackQueryService
    [EmployeeQueryService] as EmployeeQueryService
    [FeedbackCommandService] as FeedbackCommandService

    interface IEmployeeQueryService
    interface IFeedbackQueryService
    interface IFeedbackCommandService
    
    interface IFeedbackReadRepository
    interface IFeedbackWriteRepository
    interface IUnitOfWork
    interface IEmployeeApiRepository
}

package "FeedbackService.Infrastructure.DataAccessLayer" {
    [UnitOfWork] as UnitOfWork
    [FeedbackWriteRepository] as FeedbackWriteRepository
    [FeedbackReadRepository] as FeedbackReadRepository
}

package "FeedbackService.Infrastructure.Integrations" {
    [EmployeeApiRepository] as EmployeeApiRepository
}

IFeedbackQueryService -d- FeedbackQueryService
IFeedbackCommandService -d- FeedbackCommandService

IEmployeeQueryService -d- EmployeeQueryService

IUnitOfWork -d- UnitOfWork
IFeedbackReadRepository -d- FeedbackReadRepository
IFeedbackWriteRepository -d- FeedbackWriteRepository

IEmployeeApiRepository -d- EmployeeApiRepository

[WebAPI] ..( () IFeedbackQueryService
[WebAPI] ..( () IFeedbackCommandService


[FeedbackQueryService] .....( () IFeedbackReadRepository
[FeedbackQueryService] ...( () IEmployeeQueryService
[FeedbackCommandService] .....( () IFeedbackWriteRepository
[FeedbackCommandService] .....( () IUnitOfWork
[FeedbackCommandService] ...( () IEmployeeQueryService

[EmployeeQueryService] ..( () IEmployeeApiRepository

@enduml
