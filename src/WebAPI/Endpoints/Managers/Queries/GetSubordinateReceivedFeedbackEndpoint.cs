using Microsoft.AspNetCore.Mvc;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Feedback.Queries.Services;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Authorization;
using OneTalent.FeedbackService.WebAPI.DTOs;

namespace OneTalent.FeedbackService.WebAPI.Endpoints.Managers.Queries;

public static class GetSubordinateReceivedFeedbackEndpoint
{
    [Acl(Rights.ViewSubordinateFeedback)]
    internal static async Task<IResult> ExecuteAsync(
        [FromRoute] ItemId subordinateId,
        [FromRoute] ItemId id,
        IFeedbackQueryService feedbackQueryService,
        CancellationToken cancellationToken)
    {
        var feedback = await feedbackQueryService.GetSubordinateReceivedFeedbackByIdAsync(
            subordinateId, 
            id, 
            cancellationToken);

        return Results.Ok(feedback.ToFeedbackDto());
    }
}
