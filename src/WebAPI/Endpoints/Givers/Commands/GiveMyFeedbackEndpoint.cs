using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using OneTalent.FeedbackService.Application.Feedback.Commands.Services;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Authorization;
using OneTalent.FeedbackService.WebAPI.DTOs;

namespace OneTalent.FeedbackService.WebAPI.Endpoints.Givers.Commands;

public static class GiveMyFeedbackEndpoint
{
    [Acl(Rights.GiveFeedback)]
    internal static async Task<IResult> ExecuteAsync(
        [FromBody] GiveFeedbackRequest request,
        IFeedbackCommandService feedbackCommandService,
        IValidator<GiveFeedbackRequest> validator,
        CancellationToken cancellationToken)
    {
        await validator.ValidateAndThrowAsync(request, cancellationToken);

        await feedbackCommandService.GiveMyFeedbackAsync(
            request.ToCommand(),
            cancellationToken);
        
        return Results.Created();
    }
}
