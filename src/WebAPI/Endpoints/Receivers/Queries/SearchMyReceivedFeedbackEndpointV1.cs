using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using OneTalent.Common.Extensions.Paging;
using OneTalent.FeedbackService.Application.Feedback.Queries.Services;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.WebAPI.Authorization;
using OneTalent.FeedbackService.WebAPI.DTOs;
using OneTalent.FeedbackService.WebAPI.DTOs.Requests;
using OneTalent.WebAPI.Extensions.Common;
using Constants = OneTalent.FeedbackService.WebAPI.Common.Constants;

namespace OneTalent.FeedbackService.WebAPI.Endpoints.Receivers.Queries;

internal static class SearchMyReceivedFeedbackEndpointV1
{
    [Acl(Rights.ViewOwnFeedback)]
    [Obsolete("This endpoint is deprecated and will be removed in a future version. Use SearchMyReceivedFeedback instead.")]
    internal static async Task<IResult> ExecuteAsync(
        [FromBody] SearchMyReceivedFeedbackRequest? request,
        IValidator<IPageRequest> validator,
        IPagingProvider pagingProvider,
        IFeedbackQueryService feedbackQueryService,
        CancellationToken cancellationToken)
    {
        request ??= new SearchMyReceivedFeedbackRequest();

        await validator.ValidateAndThrowAsync(
            request,
            cancellationToken);

        var offsetPage = pagingProvider.ParseOffsetPage(
            new PagingRequest(
                request.PageNumber ?? Constants.DefaultPageNumber,
                request.PageSize ?? 0));

        var feedbacks = await feedbackQueryService.SearchMyReceivedFeedbackAsync(
            request.ToQuery(offsetPage),
            cancellationToken);

        return Results.Ok(
            new PagedListResult<FeedbackShortDtoV1>(
                feedbacks.Paging,
                feedbacks.Items.Select(Mappers.ToReceivedFeedbackShortDtoV1)));
    }
}
