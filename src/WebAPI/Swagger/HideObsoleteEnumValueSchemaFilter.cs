using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace OneTalent.FeedbackService.WebAPI.Swagger;

// ReSharper disable once ClassNeverInstantiated.Global
internal class HideObsoleteEnumValueSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        var type = context.Type;
        if (!type.IsEnum || schema.Enum == null)
        {
            return;
        }

        var obsoleteNames = type
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            .Where(f => f.GetCustomAttribute<ObsoleteAttribute>() != null)
            .Select(f => f.Name)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        if (obsoleteNames.Count == 0)
        {
            return;
        }

        // Filter-out obsolete enum values from schema.Enum
        schema.Enum = [.. schema.Enum
            .Where(e =>
            {
                var enumString = (e as Microsoft.OpenApi.Any.OpenApiString)?.Value ?? string.Empty;
                return !obsoleteNames.Contains(enumString);
            })];
    }
}
