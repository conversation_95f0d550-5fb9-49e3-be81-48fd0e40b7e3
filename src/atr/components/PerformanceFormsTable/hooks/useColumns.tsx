import { useMemo } from 'react';

import { DataColumnProps } from '@epam/uui-core';
import {
  ContextMenuTrigger,
  ContextualMenu,
  ContextualMenuList,
  ContextualMenuListItem,
  IconButton,
  TableUserInfoCell,
  Tag
} from '@ot/onetalent-ui-kit';
import compact from 'lodash/compact';

import { ColumnGroup } from '@/atr/components/PerformanceFormsTable/hooks/useColumnGroups';
import { PerformanceFormModel } from '@/atr/domain';
import { CellContent, UserInfo, UserInfoSize } from '@/shared/components';
import { Drawer } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';
import { useFeatureFlag } from '@/shared/modules/featureFlags';
import { formatDateToDateTime } from '@/shared/utils';

import { getTagVariantByMajorStatus } from '../utils';

enum Column {
  AtrFormId = 'AtrFormId',
  AtrTemplateName = 'AtrTemplateName',
  AtrFormMajorStatus = 'AtrFormMajorStatus',
  AtrFormMinorStatus = 'AtrFormMinorStatus',
  EmployeeID = 'EmployeeID',
  EmployeeFullName = 'EmployeeFullName',
  EmployeeCompanyName = 'EmployeeCompanyName',
  EmployeeDirectorate = 'EmployeeDirectorate',
  EmployeeFunction = 'EmployeeFunction',
  EmployeeDivision = 'EmployeeDivision',
  ATRGroupName = 'ATRGroupName',
  AssessmentLineManagerID = 'AssessmentLineManagerID',
  AssessmentLineManagerFullName = 'AssessmentLineManagerFullName',
  AssessmentB2BManagerID = 'AssessmentB2BManagerID',
  AssessmentB2BManagerFullName = 'AssessmentB2BManagerFullName',
  DottedLineManagerID = 'DottedLineManagerID',
  DottedLineManagerFullName = 'DottedLineManagerFullName',
  LastUpdated = 'LastUpdated',
  UpdatedBy = 'UpdatedBy',
  Menu = 'Menu'
}

const getColumnDisplayName = (columnName: Column) => {
  switch (columnName) {
    case Column.AtrTemplateName:
    case Column.ATRGroupName:
      return 'Name';
    case Column.AtrFormMajorStatus:
      return 'Major';
    case Column.AtrFormMinorStatus:
      return 'Minor';
    case Column.EmployeeCompanyName:
      return 'Company';
    case Column.EmployeeDirectorate:
      return 'Directorate';
    case Column.EmployeeFunction:
      return 'Function';
    case Column.EmployeeDivision:
      return 'Division';
    case Column.AtrFormId:
    case Column.EmployeeID:
    case Column.AssessmentLineManagerID:
    case Column.AssessmentB2BManagerID:
    case Column.DottedLineManagerID:
      return 'ID';
    case Column.EmployeeFullName:
    case Column.AssessmentLineManagerFullName:
    case Column.AssessmentB2BManagerFullName:
    case Column.DottedLineManagerFullName:
      return 'Full Name';
    case Column.LastUpdated:
      return 'Time & Date';
    case Column.UpdatedBy:
      return 'User Name';
    default:
      return '';
  }
};

export const useColumns = (): DataColumnProps<PerformanceFormModel>[] => {
  const { toggleDrawer } = useToggleVisibility();
  const isAtrAuditLogFeatureEnabled = useFeatureFlag('Admin.ATR.AuditLog');

  return useMemo(
    () =>
      compact([
        {
          key: Column.AtrFormId,
          caption: getColumnDisplayName(Column.AtrFormId),
          group: ColumnGroup.AtrForm,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.id,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.id}
            </CellContent>
          )
        },
        {
          key: Column.AtrTemplateName,
          caption: getColumnDisplayName(Column.AtrTemplateName),
          group: ColumnGroup.AtrTemplate,
          width: 280,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.templateName,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.templateName}
            </CellContent>
          )
        },
        {
          key: Column.AtrFormMajorStatus,
          caption: getColumnDisplayName(Column.AtrFormMajorStatus),
          group: ColumnGroup.AtrFormStatus,
          width: 200,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.majorStatus,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              <Tag variant={getTagVariantByMajorStatus(form.majorStatus)}>
                {form.majorStatus}
              </Tag>
            </CellContent>
          )
        },
        {
          key: Column.AtrFormMinorStatus,
          caption: getColumnDisplayName(Column.AtrFormMinorStatus),
          group: ColumnGroup.AtrFormStatus,
          width: 160,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.minorStatus,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.minorStatus}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeID,
          caption: getColumnDisplayName(Column.EmployeeID),
          group: ColumnGroup.Employee,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employee.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.employee.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeFullName,
          caption: getColumnDisplayName(Column.EmployeeFullName),
          group: ColumnGroup.Employee,
          width: 200,
          render: (form) => {
            const description = ['Designer', 'ADNOC']
              .filter((item) => !!item)
              .join(', ');
            console.log({
              description,
              clamp: description ?? false ? 1 : 2
            });
            return (
              <CellContent>
                {form.employee.fullNameEnglish === 'Abat Kassymov' ? (
                  <UserInfo
                    size={UserInfoSize.ExtraSmall}
                    email={form.employee.email}
                    fullName={form.employee.fullNameEnglish}
                    companyName={form.employee.companyName}
                    positionName={form.employee.positionName}
                    titleClassName="text-body-2-regular"
                    hasTooltip
                    isTitleTruncated
                    isSubtitleTruncated
                  />
                ) : (
                  <TableUserInfoCell
                    company="ADNOC"
                    email="<EMAIL>"
                    fullName="Bobinious Williamsonson"
                    position="Designer"
                    size="Small"
                    className="!px-0"
                    tooltipMode="always"
                  />
                )}
              </CellContent>
            );
          }
        },
        {
          key: Column.EmployeeCompanyName,
          caption: getColumnDisplayName(Column.EmployeeCompanyName),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeCompanyName,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeCompanyName}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeDirectorate,
          caption: getColumnDisplayName(Column.EmployeeDirectorate),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeDirectorate,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeDirectorate}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeFunction,
          caption: getColumnDisplayName(Column.EmployeeFunction),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeFunction,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeFunction}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeDivision,
          caption: getColumnDisplayName(Column.EmployeeDivision),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeDivision,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeDivision}
            </CellContent>
          )
        },
        {
          key: Column.ATRGroupName,
          caption: getColumnDisplayName(Column.ATRGroupName),
          group: ColumnGroup.ATRGroup,
          width: 284,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.atrGroupName,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.atrGroupName}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentLineManagerID,
          caption: getColumnDisplayName(Column.AssessmentLineManagerID),
          group: ColumnGroup.AssessmentManagerLM,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.assessmentLineManager.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.assessmentLineManager.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentLineManagerFullName,
          caption: getColumnDisplayName(Column.AssessmentLineManagerFullName),
          group: ColumnGroup.AssessmentManagerLM,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.assessmentLineManager.fullNameEnglish ? (
                <UserInfo
                  size={UserInfoSize.ExtraSmall}
                  email={form.assessmentLineManager.email}
                  fullName={form.assessmentLineManager.fullNameEnglish}
                  companyName={form.assessmentLineManager.companyName}
                  positionName={form.assessmentLineManager.positionName}
                  titleClassName="text-body-2-regular"
                  hasTooltip
                  isTitleTruncated
                  isSubtitleTruncated
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentB2BManagerID,
          caption: getColumnDisplayName(Column.AssessmentB2BManagerID),
          group: ColumnGroup.AssessmentManagerB2B,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.assessmentB2BManager?.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.assessmentB2BManager?.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentB2BManagerFullName,
          caption: getColumnDisplayName(Column.AssessmentB2BManagerFullName),
          group: ColumnGroup.AssessmentManagerB2B,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.assessmentB2BManager?.fullNameEnglish ? (
                <UserInfo
                  size={UserInfoSize.ExtraSmall}
                  email={form.assessmentB2BManager.email}
                  fullName={form.assessmentB2BManager.fullNameEnglish}
                  companyName={form.assessmentB2BManager.companyName}
                  positionName={form.assessmentB2BManager.positionName}
                  titleClassName="text-body-2-regular"
                  hasTooltip
                  isTitleTruncated
                  isSubtitleTruncated
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.DottedLineManagerID,
          caption: getColumnDisplayName(Column.DottedLineManagerID),
          group: ColumnGroup.DottedLineManager,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.dottedLineManager?.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.dottedLineManager?.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.DottedLineManagerFullName,
          caption: getColumnDisplayName(Column.DottedLineManagerFullName),
          group: ColumnGroup.DottedLineManager,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.dottedLineManager?.fullNameEnglish ? (
                <UserInfo
                  size={UserInfoSize.ExtraSmall}
                  email={form.dottedLineManager.email}
                  fullName={form.dottedLineManager.fullNameEnglish}
                  companyName={form.dottedLineManager.companyName}
                  positionName={form.dottedLineManager.positionName}
                  titleClassName="text-body-2-regular"
                  hasTooltip
                  isTitleTruncated
                  isSubtitleTruncated
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.LastUpdated,
          caption: getColumnDisplayName(Column.LastUpdated),
          group: ColumnGroup.LastUpdated,
          width: 120,
          render: (form) => {
            const formattedDate = form.lastUpdated
              ? formatDateToDateTime(form.lastUpdated)
              : '';
            return (
              <CellContent
                tooltipProps={{
                  title: formattedDate,
                  className:
                    '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
                }}
                classNames="line-clamp-2"
              >
                {formattedDate}
              </CellContent>
            );
          }
        },
        {
          key: Column.UpdatedBy,
          caption: getColumnDisplayName(Column.UpdatedBy),
          group: ColumnGroup.UpdatedBy,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.updatedBy ? (
                <UserInfo
                  size={UserInfoSize.ExtraSmall}
                  email={form.updatedBy.email}
                  fullName={form.updatedBy.fullNameEnglish}
                  companyName={form.updatedBy.companyName}
                  positionName={form.updatedBy.positionName}
                  titleClassName="text-body-2-regular"
                  hasTooltip
                  isTitleTruncated
                  isSubtitleTruncated
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        isAtrAuditLogFeatureEnabled && {
          key: Column.Menu,
          width: 72,
          minWidth: 72,
          render: (form) => (
            <CellContent>
              <ContextualMenu dataAttributes="ContextualMenu">
                <ContextMenuTrigger>
                  <IconButton icon="Kebab" variant="Tertiary" />
                </ContextMenuTrigger>
                <ContextualMenuList>
                  <ContextualMenuListItem
                    onClick={() => {
                      toggleDrawer({
                        name: Drawer.AuditLogDrawer,
                        performanceFormId: form.id
                      });
                    }}
                  >
                    Audit Log
                  </ContextualMenuListItem>
                </ContextualMenuList>
              </ContextualMenu>
            </CellContent>
          )
        }
      ]),
    [isAtrAuditLogFeatureEnabled, toggleDrawer]
  );
};
