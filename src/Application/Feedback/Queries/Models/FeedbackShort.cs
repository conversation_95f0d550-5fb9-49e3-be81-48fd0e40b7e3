using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Employees.Queries.Models;
using OneTalent.FeedbackService.Application.FeedbackSubjects.Queries.Models;
using OneTalent.FeedbackService.Contracts.Constants;

namespace OneTalent.FeedbackService.Application.Feedback.Queries.Models;

public record FeedbackShort(
    ItemId Id,
    IEmployee? Giver,
    IEmployee? Receiver,
    IEmployee? Requestor,
    FeedbackTopics Topic,
    ISubject Subject,
    byte? Rating,
    DateTimeOffset? RequestedDate,
    DateTimeOffset? CompletedDate,
    string? RequestDetails,
    string? FeedbackText,
    FeedbackStatuses Status,
    bool IsRequestedByManager,
    bool IsVisibleToManager,
    FeedbackTags[]? Tags)
{
    public IEmployee? Giver { get; set; } = Giver;
    public IEmployee? Receiver { get; set; } = Receiver;
    public IEmployee? Requestor { get; set; } = Requestor;
    public ISubject Subject { get; set; } = Subject;
    public FeedbackTags[]? Tags { get; set; } = Tags;
}
