using OneTalent.Common.Extensions.Paging;
using OneTalent.Common.Extensions.Sorting;
using OneTalent.FeedbackService.Application.Common.Models;

namespace OneTalent.FeedbackService.Application.Feedback.Queries.Models;

public record ReceivedFeedbackQueryOptions : PagedQueryOptions<ReceivedFeedbackFilter>
{
    public ReceivedFeedbackQueryOptions(ReceivedFeedbackFilter Filter, OrderBy? OrderBy, OffsetPage OffsetPage)
        : base(Filter, OrderBy, OffsetPage)
    {
    }
}
