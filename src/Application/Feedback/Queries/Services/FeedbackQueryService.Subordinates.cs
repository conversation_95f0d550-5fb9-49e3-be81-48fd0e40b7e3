using OneTalent.Common.Extensions.Exceptions;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using OneTalent.Common.Extensions.Sorting;
using OneTalent.FeedbackService.Application.Admin.Queries.Models;
using OneTalent.FeedbackService.Application.Employees.Queries.Models;
using OneTalent.FeedbackService.Application.Feedback.Queries.Models;
using OneTalent.FeedbackService.Contracts.Constants;
using FeedbackModel = OneTalent.FeedbackService.Application.Feedback.Queries.Models.Feedback;

namespace OneTalent.FeedbackService.Application.Feedback.Queries.Services;

internal sealed partial class FeedbackQueryService
{
    private record MatchedFeedbackTemplate(ItemId? ReceiverId, ItemId? GiverId, FeedbackShort Feedback, bool AllowEmployeeToChangeFeedbackVisibility);

    public async Task<PagedListResult<FeedbackShort>> SearchMySubordinateReceivedFeedbackAsync(
        SearchMySubordinateReceivedFeedbackQuery query,
        CancellationToken cancellationToken)
    {
        var (employee, currentAtrCycle) = await GetEmployeeAndCurrentAtrCycleAsync(cancellationToken);

        var template = await adminQueryService.GetAtrTemplateByEmployeeIdAsync(query.SubordinateId, cancellationToken);

        var feedback = await feedbackReadRepository.GetSubordinateReceivedFeedbackAsync(new (
            new (
                ReceiverId: query.SubordinateId,
                AtrCycleId: currentAtrCycle.Id,
                Status: FeedbackStatuses.Completed,
                IsVisibleToManager: true,
                AllowEmployeeToChangeFeedbackVisibility: template.AllowEmployeeToChangeFeedbackVisibility
            ),
            new OrderBy(FieldNames.CompletedDate, SortDirection.Descending),
            query.OffsetPage
        ), cancellationToken);

        var items = feedback.Items.ToArray();

        if (items.Length == 0)
        {
            return feedback;
        }

        await LoadDataAsync(items, LoadOption.LoadGiver, cancellationToken);

        return new PagedListResult<FeedbackShort>(
            feedback.Paging,
            items
        );
    }

    public async Task<PagedListResult<FeedbackShort>> SearchMySubordinateGivenFeedbackAsync(
        SearchMySubordinateGivenFeedbackQuery query,
        CancellationToken cancellationToken)
    {
        var (employee, currentAtrCycle) = await GetEmployeeAndCurrentAtrCycleAsync(cancellationToken);

        var subordinateIds = (await employeeQueryService.GetAllDirectSubordinatesAsync(
                employee.EmployeeId,
                cancellationToken))
            .Select(x => x.EmployeeId)
            .ToArray();

        var feedback = await feedbackReadRepository.GetSubordinateGivenFeedbackAsync(new(
                CurrentEmployeeId: employee.EmployeeId,
                GiverId: query.SubordinateId,
                AtrCycleId: currentAtrCycle.Id,
                Status: FeedbackStatuses.Completed,
                IsVisibleToReceiver: true
            ), cancellationToken);

        var receivers = feedback.DistinctBy(x => (x.Receiver as EmployeeBase)?.EmployeeId)
            .Select(x => (x.Receiver as EmployeeBase)?.EmployeeId)
            .OfType<ItemId>()
            .Intersect(subordinateIds)
            .ToArray();

        var templates = await adminQueryService.GetAtrTemplatesByEmployeeIdsAsync(receivers, cancellationToken);

        var filteredFeedback = MatchFeedbackWithTemplate(feedback, templates)
           .Where(x => !x.AllowEmployeeToChangeFeedbackVisibility || x.Feedback.IsVisibleToManager ||
                (x.ReceiverId != null && subordinateIds != null &&
                    !subordinateIds.Contains(x.ReceiverId!.Value)))
           .Select(x => x.Feedback)     
           .ToArray();

        filteredFeedback = WithSorting(
            filteredFeedback, 
            new OrderBy(FieldNames.CompletedDate, SortDirection.Descending)
            ).ToArray();

        var pagedItems = filteredFeedback
            .Skip((query.OffsetPage.PageNumber - 1) * query.OffsetPage.PageSize)
            .Take(query.OffsetPage.PageSize)
            .ToArray();

        await LoadDataAsync(pagedItems, LoadOption.LoadReceiver, cancellationToken);

        return new PagedListResult<FeedbackShort>(
            query.OffsetPage,
            pagedItems,
            filteredFeedback?.Length ?? 0
        );
    }

    private static Func<FeedbackShort, object> SortExpression(string field) =>
      field switch
      {
          FieldNames.CompletedDate => s => s.CompletedDate ?? DateTimeOffset.MinValue,
          _ => s => s.Id.ToRawId()
      };

    private static IEnumerable<FeedbackShort> WithSorting(IEnumerable<FeedbackShort> query, OrderBy? orderBy)
    {
        if (orderBy != null)
        {
            query = orderBy.Direction switch
            {
                SortDirection.Ascending => query
                    .OrderBy(SortExpression(orderBy.Field))
                    .ThenBy(o => o.Id.ToRawId()),
                SortDirection.Descending => query
                    .OrderByDescending(SortExpression(orderBy.Field))
                    .ThenByDescending(o => o.Id.ToRawId()),
                _ => query
            };
        }

        return query;
    }

    public async Task<PagedListResult<EmployeeWithFeedbackInfo>> GetDirectSubordinatesWithFeedbackInfoAsync(
       DirectSubordinatesQuery query,
       CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentEmployeeAsync(cancellationToken);

        var queryOptions = new DirectSubordinatesQueryOptions(
            new DirectSubordinatesFilter(employee.EmployeeId),
            null,
            query.OffsetPage
        );

        var pagedEmployees = await employeeQueryService.GetDirectSubordinatesAsync(queryOptions, cancellationToken);

        var currentAtrCycle = await adminQueryService.GetCurrentAtrCycleAsync(cancellationToken);

        var employeeIds = pagedEmployees.Items.Select(e => e.EmployeeId).ToArray();

        var subordinateIds = (await employeeQueryService.GetAllDirectSubordinatesAsync(
                employee.EmployeeId,
                cancellationToken))
            .Select(x => x.EmployeeId)
            .ToArray();

        var filter = new FeedbackInfoFilter(
                employeeIds,
                currentAtrCycle.Id,
                subordinateIds,
                employee.EmployeeId);

        var feedbackGivenInfo = await GetFeedbackGivenInfoByEmployeeIdsAsync(filter, cancellationToken);

        var feedbackReceivedInfo = await GetFeedbackReceivedInfoByEmployeeIdsAsync(filter, cancellationToken);

        var employeesWithFeedbackInfo = pagedEmployees.Items
            .Select(employee =>
            {
                var info = new FeedbackInfo(
                    feedbackGivenInfo.TryGetValue(employee.EmployeeId, out var given) ? given.Count : 0,
                    feedbackReceivedInfo.TryGetValue(employee.EmployeeId, out var received) ? received.Count : 0);

                return employee.ToEmployeeWithFeedbackInfo(info);
            }).ToList();

        return new PagedListResult<EmployeeWithFeedbackInfo>(
            pagedEmployees.Paging,
            employeesWithFeedbackInfo
        );
    }

    private async Task<Dictionary<ItemId, FeedbackCountInfo>> GetFeedbackGivenInfoByEmployeeIdsAsync(FeedbackInfoFilter filter, CancellationToken cancellationToken)
    {
        var givenFeedback = await feedbackReadRepository.GetGivenFeedbackByEmployeeIdsAsync(filter, cancellationToken);

        var receivers = givenFeedback.DistinctBy(x => (x.Receiver as EmployeeBase)?.EmployeeId)
            .Select(x => (x.Receiver as EmployeeBase)?.EmployeeId)
            .OfType<ItemId>()
            .Intersect(filter.SubordinateIds)
            .ToArray();

        var templates = await adminQueryService.GetAtrTemplatesByEmployeeIdsAsync(receivers, cancellationToken);

        return MatchFeedbackWithTemplate(givenFeedback, templates)
            .Where(x => !x.AllowEmployeeToChangeFeedbackVisibility || x.Feedback.IsVisibleToManager ||
                (x.ReceiverId != null && filter.SubordinateIds != null &&
                    !filter.SubordinateIds.Contains(x.ReceiverId!.Value))) //retrieve all given feedback, excluding hidden from manager feedback where receivers are managers subordinates
            .GroupBy(x => x.GiverId)
            .Select(g => new FeedbackCountInfo(
                g.Key!.Value,
                g.Count()))
            .ToDictionary(x => x.EmployeeId);
    }

    private async Task<Dictionary<ItemId, FeedbackCountInfo>> GetFeedbackReceivedInfoByEmployeeIdsAsync(FeedbackInfoFilter filter, CancellationToken cancellationToken)
    {
        var templates = await adminQueryService.GetAtrTemplatesByEmployeeIdsAsync(filter.SubordinateIds, cancellationToken);

        var receivedFeedback = await feedbackReadRepository.GetReceivedFeedbackByEmployeeIdsAsync(filter, cancellationToken);

        return MatchFeedbackWithTemplate(receivedFeedback, templates)
            .Where(x => !x.AllowEmployeeToChangeFeedbackVisibility || x.Feedback.IsVisibleToManager)
            .GroupBy(x => x.ReceiverId)
            .Select(g => new FeedbackCountInfo(
                g.Key!.Value,
                g.Count()))
            .ToDictionary(x => x.EmployeeId);
    }
    private static IEnumerable<MatchedFeedbackTemplate> MatchFeedbackWithTemplate(FeedbackShort[] feedback, Dictionary<ItemId, AtrTemplate> templates) =>
        feedback.GroupJoin(
            templates,
            g => (g.Receiver as EmployeeBase)?.EmployeeId,
            t => t.Key,
            (feedback, template) => new { feedback, Template = template.Select(x => x.Value) })
           .SelectMany(
               joinedSet => joinedSet.Template.DefaultIfEmpty(),
               (g, t) => new MatchedFeedbackTemplate(
                    (g.feedback.Receiver as EmployeeBase)?.EmployeeId,
                    (g.feedback.Giver as EmployeeBase)?.EmployeeId,
                    g.feedback,
                    t?.AllowEmployeeToChangeFeedbackVisibility ?? false
               ));

    public async Task<FeedbackModel> GetSubordinateGivenFeedbackByIdAsync(ItemId subordinateId, ItemId id, CancellationToken cancellationToken)
    {
        var manager = await employeeQueryService.GetCurrentEmployeeAsync(cancellationToken);

        var feedback = await GetFeedbackByIdAsync(id, cancellationToken);

        await EnsureManagerCanViewFeedbackAsync(manager.EmployeeId, feedback, cancellationToken);
        EnsureUserIsFeedbackGiver(subordinateId, feedback);
        EnsureManagerIsNotFeedbackReceiver(feedback, manager.EmployeeId);

        feedback.Requestor = await LoadEmployeeAsync(feedback.Requestor, feedback.Id, cancellationToken);
        feedback.Giver = await LoadEmployeeAsync(feedback.Giver, feedback.Id, cancellationToken);
        feedback.Receiver = await LoadEmployeeAsync(feedback.Receiver, feedback.Id, cancellationToken);
        feedback.Subject = await LoadSubjectAsync(feedback.Subject, feedback.Receiver, cancellationToken);

        return feedback;
    }

    public async Task<FeedbackModel> GetSubordinateReceivedFeedbackByIdAsync(ItemId subordinateId, ItemId id, CancellationToken cancellationToken)
    {
        var manager = await employeeQueryService.GetCurrentEmployeeAsync(cancellationToken);

        var feedback = await GetFeedbackByIdAsync(id, cancellationToken);

        await EnsureManagerCanViewFeedbackAsync(feedback, cancellationToken);
        EnsureUserIsFeedbackReceiver(subordinateId, feedback);
        EnsureManagerIsNotFeedbackReceiver(feedback, manager.EmployeeId);

        feedback.Requestor = await LoadEmployeeAsync(feedback.Requestor, feedback.Id, cancellationToken);
        feedback.Giver = await LoadEmployeeAsync(feedback.Giver, feedback.Id, cancellationToken);
        feedback.Receiver = await LoadEmployeeAsync(feedback.Receiver, feedback.Id, cancellationToken);
        feedback.Subject = await LoadSubjectAsync(feedback.Subject, feedback.Receiver, cancellationToken);

        return feedback;
    }

    public async Task<SubordinateFeedbackOverview> GetSubordinateFeedbackOverviewAsync(ItemId subordinateId, CancellationToken cancellationToken)
    {
        var currentAtrCycle = await adminQueryService.GetCurrentAtrCycleAsync(cancellationToken);

        var currentUser = await employeeQueryService.GetCurrentEmployeeAsync(cancellationToken);

        var subordinateTemplate = await adminQueryService.GetAtrTemplateByEmployeeIdAsync(subordinateId, cancellationToken);

        var receivedCount = await feedbackReadRepository.GetSubordinateReceivedFeedbackCountAsync(new(
                subordinateId,
                currentAtrCycle.Id,
                FeedbackStatuses.Completed,
                IsVisibleToManager: true,
                subordinateTemplate.AllowEmployeeToChangeFeedbackVisibility
            ), cancellationToken);

        var givenCount = await GetSubordinateFeedbackGivenOverviewAsync(new(
                currentUser.EmployeeId,
                subordinateId,
                currentAtrCycle.Id,
                FeedbackStatuses.Completed,
                IsVisibleToReceiver: true
            ), cancellationToken);

       return new SubordinateFeedbackOverview(receivedCount, givenCount);
    }

    private async Task<int> GetSubordinateFeedbackGivenOverviewAsync(
        GivenFeedbackFilter filter,
        CancellationToken cancellationToken)
    {
        var givenFeedback = await feedbackReadRepository.GetSubordinateGivenFeedbackAsync(filter, cancellationToken);

        var subordinateIds = (await employeeQueryService.GetAllDirectSubordinatesAsync(
                filter.CurrentEmployeeId,
                cancellationToken))
            .Select(x => x.EmployeeId)
            .ToArray();

        var receivers = givenFeedback.DistinctBy(x => (x.Receiver as EmployeeBase)?.EmployeeId)
            .Select(x => (x.Receiver as EmployeeBase)?.EmployeeId)
            .OfType<ItemId>()
            .Intersect(subordinateIds)
            .ToArray();

        var templates = await adminQueryService.GetAtrTemplatesByEmployeeIdsAsync(receivers, cancellationToken);

        return MatchFeedbackWithTemplate(givenFeedback, templates)
            .Count(x => !x.AllowEmployeeToChangeFeedbackVisibility || x.Feedback.IsVisibleToManager ||
                (x.ReceiverId != null && subordinateIds != null &&
                    !subordinateIds.Contains(x.ReceiverId!.Value))); //count all given feedback, excluding hidden from manager feedback where receivers are managers subordinates
    }

    private async Task EnsureManagerCanViewFeedbackAsync(FeedbackModel feedback, CancellationToken ct)
    {
        if (feedback.Receiver is EmployeeBase receiver)
        {
            var template = await adminQueryService.GetAtrTemplateByEmployeeIdAsync(receiver.EmployeeId, ct);

            if (template.AllowEmployeeToChangeFeedbackVisibility && !feedback.IsVisibleToManager)
            {
                throw new ItemNotFoundException($"Feedback {feedback.Id} is not found.");
            }
        }
    }

    private async Task EnsureManagerCanViewFeedbackAsync(ItemId managerId, FeedbackModel feedback, CancellationToken ct)
    {
        var subordinateIds = (await employeeQueryService.GetAllDirectSubordinatesAsync(
                managerId,
                ct))
            .Select(x => x.EmployeeId)
            .ToArray();

        if(feedback.Receiver is EmployeeBase receiver)
        {
            var template = await adminQueryService.GetAtrTemplateByEmployeeIdAsync(receiver.EmployeeId, ct);

            if (template.AllowEmployeeToChangeFeedbackVisibility && !feedback.IsVisibleToManager && subordinateIds.Contains(receiver.EmployeeId))
            {
                throw new ItemNotFoundException($"Feedback {feedback.Id} is not found.");
            }
        }
    }

    private static void EnsureManagerIsNotFeedbackReceiver(FeedbackModel feedback, ItemId managerId)
    {
        if (!feedback.IsVisibleToReceiver && (feedback.Receiver as EmployeeBase)?.EmployeeId == managerId)
        {
            throw new ItemNotFoundException($"Feedback {feedback.Id} is not found.");
        }
    }
}
