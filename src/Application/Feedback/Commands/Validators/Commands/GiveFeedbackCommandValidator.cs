using FluentValidation;
using Microsoft.FeatureManagement;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Employees.Queries.Services;
using OneTalent.FeedbackService.Application.Feedback.Commands.Models.Commands;
using OneTalent.FeedbackService.Contracts.Constants;

namespace OneTalent.FeedbackService.Application.Feedback.Commands.Validators.Commands;

internal sealed class GiveFeedbackCommandValidator : AbstractValidator<GiveFeedbackCommand>
{
    private readonly IEmployeeQueryService _employeeQueryService;

    public GiveFeedbackCommandValidator(
        IEmployeeQueryService employeeQueryService,
        IFeatureManager featureManager)
    {
        _employeeQueryService = employeeQueryService;

        ClassLevelCascadeMode = CascadeMode.Stop;
        RuleLevelCascadeMode = CascadeMode.Stop;

        RuleFor(x => x.FeedbackId)
            .MustAsync(CurrentEmployeeIsGiverAsync)
            .WithMessage("You are not the giver for this feedback")

            .Must(FeedbackIsPendingOrCompleted)
            .WithMessage("An error occurred, the feedback status has already been changed. Please refresh the page")
            .DependentRules(() =>
            {
                RuleFor(x => x.FeedbackId)
                    .Must(RatingAndFeedbackTextDidNotChange)
                    .WithMessage("An error occurred, the feedback status has already been changed. Please refresh the page")
                    .When(FeedbackValidation.FeedbackIsCompleted);
            });

        RuleFor(x => x.Rating).ValidateRating(featureManager);

        RuleFor(x => x.FeedbackText).ValidateFeedbackText();
    }

    Task<bool> CurrentEmployeeIsGiverAsync(
        GiveFeedbackCommand command,
        ItemId feedbackId,
        ValidationContext<GiveFeedbackCommand> context,
        CancellationToken cancellation
    ) =>
        FeedbackValidation.CurrentEmployeeIsGiverAsync(command, feedbackId, context, _employeeQueryService, cancellation);

    private static bool FeedbackIsPendingOrCompleted(
        GiveFeedbackCommand command,
        ItemId feedbackId,
        ValidationContext<GiveFeedbackCommand> context)
    {
        var feedback = FeedbackValidation.GetFeedback(context);

        return feedback.Status is FeedbackStatuses.Pending or FeedbackStatuses.Completed;
    }

    private static bool RatingAndFeedbackTextDidNotChange(
        GiveFeedbackCommand command,
        ItemId feedbackId,
        ValidationContext<GiveFeedbackCommand> context)
    {
        var feedback = FeedbackValidation.GetFeedback(context);

        return feedback.Rating == command.Rating && feedback.FeedbackText == command.FeedbackText;
    }
}
