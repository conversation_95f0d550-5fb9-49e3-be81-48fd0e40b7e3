using OneTalent.FeedbackService.Application.Employees.Queries.Models;

namespace OneTalent.FeedbackService.Application.Surveys.Commands.Models;
internal static class Mappers
{
    public static RequestFeedbackSurvey ToRequestFeedbackSurvey(
        this SubmitFeedbackSurveyCommand command,
        Employee employee)
    {
        return new RequestFeedbackSurvey(
            command.Rate,
            command.Improvements,
            command.Comment,
            command.ProductivityGain,
            employee.EmployeeId,
            "N/A",
            employee.Email);
    }
}

