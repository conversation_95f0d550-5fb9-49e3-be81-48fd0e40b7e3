using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Contracts.Constants;

namespace OneTalent.FeedbackService.Application.Admin.Queries.Models;

public record AtrCycle(
    ItemId Id,
    string Name,
    Period Period,
    AtrCycleStatuses Status,
    AtrCycle.Phase[] Phases,
    AtrCycle.PermissionGroup[] UserGroups
)
{
    public record Phase(
        ItemId Id,
        int Order,
        string Name,
        DateOnly StartDate,
        DateOnly EndDate
    );

    public record PermissionGroup(
        ItemId Id
    );
}
