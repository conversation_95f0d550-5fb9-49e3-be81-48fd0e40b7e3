/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.133
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface EmployeeShortDto
 */
export interface EmployeeShortDto {
    /**
     * 
     * @type {string}
     * @memberof EmployeeShortDto
     */
    employeeId: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeShortDto
     */
    companyName: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeShortDto
     */
    fullNameEnglish: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeShortDto
     */
    email: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeShortDto
     */
    positionName: string;
}

/**
 * Check if a given object implements the EmployeeShortDto interface.
 */
export function instanceOfEmployeeShortDto(value: object): value is EmployeeShortDto {
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    if (!('companyName' in value) || value['companyName'] === undefined) return false;
    if (!('fullNameEnglish' in value) || value['fullNameEnglish'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('positionName' in value) || value['positionName'] === undefined) return false;
    return true;
}

export function EmployeeShortDtoFromJSON(json: any): EmployeeShortDto {
    return EmployeeShortDtoFromJSONTyped(json, false);
}

export function EmployeeShortDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmployeeShortDto {
    if (json == null) {
        return json;
    }
    return {
        
        'employeeId': json['employeeId'],
        'companyName': json['companyName'],
        'fullNameEnglish': json['fullNameEnglish'],
        'email': json['email'],
        'positionName': json['positionName'],
    };
}

export function EmployeeShortDtoToJSON(value?: EmployeeShortDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'employeeId': value['employeeId'],
        'companyName': value['companyName'],
        'fullNameEnglish': value['fullNameEnglish'],
        'email': value['email'],
        'positionName': value['positionName'],
    };
}

