/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.133
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ActionType } from './ActionType';
import {
    ActionTypeFromJSON,
    ActionTypeFromJSONTyped,
    ActionTypeToJSON,
} from './ActionType';
import type { EmployeeShortDto } from './EmployeeShortDto';
import {
    EmployeeShortDtoFromJSON,
    EmployeeShortDtoFromJSONTyped,
    EmployeeShortDtoToJSON,
} from './EmployeeShortDto';
import type { ActionStatus } from './ActionStatus';
import {
    ActionStatusFromJSON,
    ActionStatusFromJSONTyped,
    ActionStatusToJSON,
} from './ActionStatus';

/**
 * 
 * @export
 * @interface ActionDto
 */
export interface ActionDto {
    /**
     * 
     * @type {string}
     * @memberof ActionDto
     */
    id?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof ActionDto
     */
    isValidation?: boolean | null;
    /**
     * 
     * @type {ActionStatus}
     * @memberof ActionDto
     */
    status: ActionStatus;
    /**
     * 
     * @type {ActionType}
     * @memberof ActionDto
     */
    type: ActionType;
    /**
     * 
     * @type {string}
     * @memberof ActionDto
     * @deprecated
     */
    initiatorId?: string | null;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof ActionDto
     */
    initiator?: EmployeeShortDto;
    /**
     * 
     * @type {Date}
     * @memberof ActionDto
     */
    startDate?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof ActionDto
     */
    endDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof ActionDto
     */
    inputFileId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ActionDto
     */
    inputFileName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ActionDto
     */
    inputFileSize?: number | null;
    /**
     * 
     * @type {string}
     * @memberof ActionDto
     */
    outputFileName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ActionDto
     */
    totalRowsProcessed: number;
    /**
     * 
     * @type {number}
     * @memberof ActionDto
     */
    rowsSucceeded: number;
    /**
     * 
     * @type {number}
     * @memberof ActionDto
     */
    rowsFailed: number;
}



/**
 * Check if a given object implements the ActionDto interface.
 */
export function instanceOfActionDto(value: object): value is ActionDto {
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('type' in value) || value['type'] === undefined) return false;
    if (!('totalRowsProcessed' in value) || value['totalRowsProcessed'] === undefined) return false;
    if (!('rowsSucceeded' in value) || value['rowsSucceeded'] === undefined) return false;
    if (!('rowsFailed' in value) || value['rowsFailed'] === undefined) return false;
    return true;
}

export function ActionDtoFromJSON(json: any): ActionDto {
    return ActionDtoFromJSONTyped(json, false);
}

export function ActionDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'isValidation': json['isValidation'] == null ? undefined : json['isValidation'],
        'status': ActionStatusFromJSON(json['status']),
        'type': ActionTypeFromJSON(json['type']),
        'initiatorId': json['initiatorId'] == null ? undefined : json['initiatorId'],
        'initiator': json['initiator'] == null ? undefined : EmployeeShortDtoFromJSON(json['initiator']),
        'startDate': json['startDate'] == null ? undefined : (new Date(json['startDate'])),
        'endDate': json['endDate'] == null ? undefined : (new Date(json['endDate'])),
        'inputFileId': json['inputFileId'] == null ? undefined : json['inputFileId'],
        'inputFileName': json['inputFileName'] == null ? undefined : json['inputFileName'],
        'inputFileSize': json['inputFileSize'] == null ? undefined : json['inputFileSize'],
        'outputFileName': json['outputFileName'] == null ? undefined : json['outputFileName'],
        'totalRowsProcessed': json['totalRowsProcessed'],
        'rowsSucceeded': json['rowsSucceeded'],
        'rowsFailed': json['rowsFailed'],
    };
}

export function ActionDtoToJSON(value?: ActionDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'isValidation': value['isValidation'],
        'status': ActionStatusToJSON(value['status']),
        'type': ActionTypeToJSON(value['type']),
        'initiatorId': value['initiatorId'],
        'initiator': EmployeeShortDtoToJSON(value['initiator']),
        'startDate': value['startDate'] == null ? undefined : ((value['startDate'] as any).toISOString()),
        'endDate': value['endDate'] == null ? undefined : ((value['endDate'] as any).toISOString()),
        'inputFileId': value['inputFileId'],
        'inputFileName': value['inputFileName'],
        'inputFileSize': value['inputFileSize'],
        'outputFileName': value['outputFileName'],
        'totalRowsProcessed': value['totalRowsProcessed'],
        'rowsSucceeded': value['rowsSucceeded'],
        'rowsFailed': value['rowsFailed'],
    };
}

