/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetSkillGroupsBySkillRequest
 */
export interface GetSkillGroupsBySkillRequest {
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    nameAr?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    description: string;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    descriptionAr?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    scaleId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    statusId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    sourceId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    typeId: string;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    lastApprovedBy?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof GetSkillGroupsBySkillRequest
     */
    validFrom?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof GetSkillGroupsBySkillRequest
     */
    validTo?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    additionalText?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    additionalDate?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetSkillGroupsBySkillRequest
     */
    additionalNumber?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetSkillGroupsBySkillRequest
     */
    subDisciplineIds: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetSkillGroupsBySkillRequest
     */
    subFamilyIds: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetSkillGroupsBySkillRequest
     */
    libraryIds: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetSkillGroupsBySkillRequest
     */
    tagIds: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetSkillGroupsBySkillRequest
     */
    companyIds: Array<string>;
}

/**
 * Check if a given object implements the GetSkillGroupsBySkillRequest interface.
 */
export function instanceOfGetSkillGroupsBySkillRequest(value: object): value is GetSkillGroupsBySkillRequest {
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('description' in value) || value['description'] === undefined) return false;
    if (!('typeId' in value) || value['typeId'] === undefined) return false;
    if (!('subDisciplineIds' in value) || value['subDisciplineIds'] === undefined) return false;
    if (!('subFamilyIds' in value) || value['subFamilyIds'] === undefined) return false;
    if (!('libraryIds' in value) || value['libraryIds'] === undefined) return false;
    if (!('tagIds' in value) || value['tagIds'] === undefined) return false;
    if (!('companyIds' in value) || value['companyIds'] === undefined) return false;
    return true;
}

export function GetSkillGroupsBySkillRequestFromJSON(json: any): GetSkillGroupsBySkillRequest {
    return GetSkillGroupsBySkillRequestFromJSONTyped(json, false);
}

export function GetSkillGroupsBySkillRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetSkillGroupsBySkillRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'nameAr': json['nameAr'] == null ? undefined : json['nameAr'],
        'description': json['description'],
        'descriptionAr': json['descriptionAr'] == null ? undefined : json['descriptionAr'],
        'scaleId': json['scaleId'] == null ? undefined : json['scaleId'],
        'statusId': json['statusId'] == null ? undefined : json['statusId'],
        'sourceId': json['sourceId'] == null ? undefined : json['sourceId'],
        'typeId': json['typeId'],
        'lastApprovedBy': json['lastApprovedBy'] == null ? undefined : json['lastApprovedBy'],
        'validFrom': json['validFrom'] == null ? undefined : (new Date(json['validFrom'])),
        'validTo': json['validTo'] == null ? undefined : (new Date(json['validTo'])),
        'additionalText': json['additionalText'] == null ? undefined : json['additionalText'],
        'additionalDate': json['additionalDate'] == null ? undefined : json['additionalDate'],
        'additionalNumber': json['additionalNumber'] == null ? undefined : json['additionalNumber'],
        'subDisciplineIds': json['subDisciplineIds'],
        'subFamilyIds': json['subFamilyIds'],
        'libraryIds': json['libraryIds'],
        'tagIds': json['tagIds'],
        'companyIds': json['companyIds'],
    };
}

export function GetSkillGroupsBySkillRequestToJSON(json: any): GetSkillGroupsBySkillRequest {
    return GetSkillGroupsBySkillRequestToJSONTyped(json, false);
}

export function GetSkillGroupsBySkillRequestToJSONTyped(value?: GetSkillGroupsBySkillRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'name': value['name'],
        'nameAr': value['nameAr'],
        'description': value['description'],
        'descriptionAr': value['descriptionAr'],
        'scaleId': value['scaleId'],
        'statusId': value['statusId'],
        'sourceId': value['sourceId'],
        'typeId': value['typeId'],
        'lastApprovedBy': value['lastApprovedBy'],
        'validFrom': value['validFrom'] == null ? undefined : ((value['validFrom'] as any).toISOString()),
        'validTo': value['validTo'] == null ? undefined : ((value['validTo'] as any).toISOString()),
        'additionalText': value['additionalText'],
        'additionalDate': value['additionalDate'],
        'additionalNumber': value['additionalNumber'],
        'subDisciplineIds': value['subDisciplineIds'],
        'subFamilyIds': value['subFamilyIds'],
        'libraryIds': value['libraryIds'],
        'tagIds': value['tagIds'],
        'companyIds': value['companyIds'],
    };
}

