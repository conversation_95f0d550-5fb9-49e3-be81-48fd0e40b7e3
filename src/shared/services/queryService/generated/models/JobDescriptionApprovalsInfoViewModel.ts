/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { WorkflowStepViewModel } from './WorkflowStepViewModel';
import {
    WorkflowStepViewModelFromJSON,
    WorkflowStepViewModelFromJSONTyped,
    WorkflowStepViewModelToJSON,
    WorkflowStepViewModelToJSONTyped,
} from './WorkflowStepViewModel';
import type { RequestHistoryEntryViewModel } from './RequestHistoryEntryViewModel';
import {
    RequestHistoryEntryViewModelFromJSON,
    RequestHistoryEntryViewModelFromJSONTyped,
    RequestHistoryEntryViewModelToJSON,
    RequestHistoryEntryViewModelToJSONTyped,
} from './RequestHistoryEntryViewModel';

/**
 * 
 * @export
 * @interface JobDescriptionApprovalsInfoViewModel
 */
export interface JobDescriptionApprovalsInfoViewModel {
    /**
     * 
     * @type {Array<WorkflowStepViewModel>}
     * @memberof JobDescriptionApprovalsInfoViewModel
     */
    approvalChain: Array<WorkflowStepViewModel>;
    /**
     * 
     * @type {Array<RequestHistoryEntryViewModel>}
     * @memberof JobDescriptionApprovalsInfoViewModel
     */
    history: Array<RequestHistoryEntryViewModel>;
}

/**
 * Check if a given object implements the JobDescriptionApprovalsInfoViewModel interface.
 */
export function instanceOfJobDescriptionApprovalsInfoViewModel(value: object): value is JobDescriptionApprovalsInfoViewModel {
    if (!('approvalChain' in value) || value['approvalChain'] === undefined) return false;
    if (!('history' in value) || value['history'] === undefined) return false;
    return true;
}

export function JobDescriptionApprovalsInfoViewModelFromJSON(json: any): JobDescriptionApprovalsInfoViewModel {
    return JobDescriptionApprovalsInfoViewModelFromJSONTyped(json, false);
}

export function JobDescriptionApprovalsInfoViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): JobDescriptionApprovalsInfoViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'approvalChain': ((json['approvalChain'] as Array<any>).map(WorkflowStepViewModelFromJSON)),
        'history': ((json['history'] as Array<any>).map(RequestHistoryEntryViewModelFromJSON)),
    };
}

export function JobDescriptionApprovalsInfoViewModelToJSON(json: any): JobDescriptionApprovalsInfoViewModel {
    return JobDescriptionApprovalsInfoViewModelToJSONTyped(json, false);
}

export function JobDescriptionApprovalsInfoViewModelToJSONTyped(value?: JobDescriptionApprovalsInfoViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'approvalChain': ((value['approvalChain'] as Array<any>).map(WorkflowStepViewModelToJSON)),
        'history': ((value['history'] as Array<any>).map(RequestHistoryEntryViewModelToJSON)),
    };
}

