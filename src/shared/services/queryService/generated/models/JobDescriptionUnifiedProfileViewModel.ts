/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { JobDescriptionStatusProfileViewModel } from './JobDescriptionStatusProfileViewModel';
import {
    JobDescriptionStatusProfileViewModelFromJSON,
    JobDescriptionStatusProfileViewModelFromJSONTyped,
    JobDescriptionStatusProfileViewModelToJSON,
    JobDescriptionStatusProfileViewModelToJSONTyped,
} from './JobDescriptionStatusProfileViewModel';
import type { KpiSectionProfileViewModel } from './KpiSectionProfileViewModel';
import {
    KpiSectionProfileViewModelFromJSON,
    KpiSectionProfileViewModelFromJSONTyped,
    KpiSectionProfileViewModelToJSON,
    KpiSectionProfileViewModelToJSONTyped,
} from './KpiSectionProfileViewModel';
import type { WorkConditionSectionProfileViewModel } from './WorkConditionSectionProfileViewModel';
import {
    WorkConditionSectionProfileViewModelFromJSON,
    WorkConditionSectionProfileViewModelFromJSONTyped,
    WorkConditionSectionProfileViewModelToJSON,
    WorkConditionSectionProfileViewModelToJSONTyped,
} from './WorkConditionSectionProfileViewModel';
import type { WorkQualificationSectionProfileViewModel } from './WorkQualificationSectionProfileViewModel';
import {
    WorkQualificationSectionProfileViewModelFromJSON,
    WorkQualificationSectionProfileViewModelFromJSONTyped,
    WorkQualificationSectionProfileViewModelToJSON,
    WorkQualificationSectionProfileViewModelToJSONTyped,
} from './WorkQualificationSectionProfileViewModel';
import type { KeyAccountabilitiesSectionProfileViewModel } from './KeyAccountabilitiesSectionProfileViewModel';
import {
    KeyAccountabilitiesSectionProfileViewModelFromJSON,
    KeyAccountabilitiesSectionProfileViewModelFromJSONTyped,
    KeyAccountabilitiesSectionProfileViewModelToJSON,
    KeyAccountabilitiesSectionProfileViewModelToJSONTyped,
} from './KeyAccountabilitiesSectionProfileViewModel';
import type { OdObjectInfoProfileViewModel } from './OdObjectInfoProfileViewModel';
import {
    OdObjectInfoProfileViewModelFromJSON,
    OdObjectInfoProfileViewModelFromJSONTyped,
    OdObjectInfoProfileViewModelToJSON,
    OdObjectInfoProfileViewModelToJSONTyped,
} from './OdObjectInfoProfileViewModel';
import type { DetailsSectionProfileViewModel } from './DetailsSectionProfileViewModel';
import {
    DetailsSectionProfileViewModelFromJSON,
    DetailsSectionProfileViewModelFromJSONTyped,
    DetailsSectionProfileViewModelToJSON,
    DetailsSectionProfileViewModelToJSONTyped,
} from './DetailsSectionProfileViewModel';
import type { DimensionsSectionProfileViewModel } from './DimensionsSectionProfileViewModel';
import {
    DimensionsSectionProfileViewModelFromJSON,
    DimensionsSectionProfileViewModelFromJSONTyped,
    DimensionsSectionProfileViewModelToJSON,
    DimensionsSectionProfileViewModelToJSONTyped,
} from './DimensionsSectionProfileViewModel';
import type { PurposeSectionProfileViewModel } from './PurposeSectionProfileViewModel';
import {
    PurposeSectionProfileViewModelFromJSON,
    PurposeSectionProfileViewModelFromJSONTyped,
    PurposeSectionProfileViewModelToJSON,
    PurposeSectionProfileViewModelToJSONTyped,
} from './PurposeSectionProfileViewModel';
import type { RelationshipSectionProfileViewModel } from './RelationshipSectionProfileViewModel';
import {
    RelationshipSectionProfileViewModelFromJSON,
    RelationshipSectionProfileViewModelFromJSONTyped,
    RelationshipSectionProfileViewModelToJSON,
    RelationshipSectionProfileViewModelToJSONTyped,
} from './RelationshipSectionProfileViewModel';
import type { SkillsManagementUnifiedSectionProfileViewModel } from './SkillsManagementUnifiedSectionProfileViewModel';
import {
    SkillsManagementUnifiedSectionProfileViewModelFromJSON,
    SkillsManagementUnifiedSectionProfileViewModelFromJSONTyped,
    SkillsManagementUnifiedSectionProfileViewModelToJSON,
    SkillsManagementUnifiedSectionProfileViewModelToJSONTyped,
} from './SkillsManagementUnifiedSectionProfileViewModel';

/**
 * 
 * @export
 * @interface JobDescriptionUnifiedProfileViewModel
 */
export interface JobDescriptionUnifiedProfileViewModel {
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    id?: string | null;
    /**
     * 
     * @type {number}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    version?: number | null;
    /**
     * 
     * @type {JobDescriptionStatusProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    status?: JobDescriptionStatusProfileViewModel;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    lastApprovedBy?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    approvedDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    modifiedBy?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    modifiedDate?: Date | null;
    /**
     * 
     * @type {OdObjectInfoProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    odObjectInfo?: OdObjectInfoProfileViewModel;
    /**
     * 
     * @type {DetailsSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    details?: DetailsSectionProfileViewModel;
    /**
     * 
     * @type {DimensionsSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    dimensions?: DimensionsSectionProfileViewModel;
    /**
     * 
     * @type {SkillsManagementUnifiedSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    skillsManagement?: SkillsManagementUnifiedSectionProfileViewModel;
    /**
     * 
     * @type {PurposeSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    purpose?: PurposeSectionProfileViewModel;
    /**
     * 
     * @type {KeyAccountabilitiesSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    keyAccountabilities?: KeyAccountabilitiesSectionProfileViewModel;
    /**
     * 
     * @type {RelationshipSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    communicationAndWorkingRelationship?: RelationshipSectionProfileViewModel;
    /**
     * 
     * @type {WorkQualificationSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    workQualification?: WorkQualificationSectionProfileViewModel;
    /**
     * 
     * @type {WorkConditionSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    workCondition?: WorkConditionSectionProfileViewModel;
    /**
     * 
     * @type {KpiSectionProfileViewModel}
     * @memberof JobDescriptionUnifiedProfileViewModel
     */
    kpi?: KpiSectionProfileViewModel;
}

/**
 * Check if a given object implements the JobDescriptionUnifiedProfileViewModel interface.
 */
export function instanceOfJobDescriptionUnifiedProfileViewModel(value: object): value is JobDescriptionUnifiedProfileViewModel {
    return true;
}

export function JobDescriptionUnifiedProfileViewModelFromJSON(json: any): JobDescriptionUnifiedProfileViewModel {
    return JobDescriptionUnifiedProfileViewModelFromJSONTyped(json, false);
}

export function JobDescriptionUnifiedProfileViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): JobDescriptionUnifiedProfileViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'version': json['version'] == null ? undefined : json['version'],
        'status': json['status'] == null ? undefined : JobDescriptionStatusProfileViewModelFromJSON(json['status']),
        'lastApprovedBy': json['lastApprovedBy'] == null ? undefined : json['lastApprovedBy'],
        'approvedDate': json['approvedDate'] == null ? undefined : (new Date(json['approvedDate'])),
        'modifiedBy': json['modifiedBy'] == null ? undefined : json['modifiedBy'],
        'modifiedDate': json['modifiedDate'] == null ? undefined : (new Date(json['modifiedDate'])),
        'odObjectInfo': json['odObjectInfo'] == null ? undefined : OdObjectInfoProfileViewModelFromJSON(json['odObjectInfo']),
        'details': json['details'] == null ? undefined : DetailsSectionProfileViewModelFromJSON(json['details']),
        'dimensions': json['dimensions'] == null ? undefined : DimensionsSectionProfileViewModelFromJSON(json['dimensions']),
        'skillsManagement': json['skillsManagement'] == null ? undefined : SkillsManagementUnifiedSectionProfileViewModelFromJSON(json['skillsManagement']),
        'purpose': json['purpose'] == null ? undefined : PurposeSectionProfileViewModelFromJSON(json['purpose']),
        'keyAccountabilities': json['keyAccountabilities'] == null ? undefined : KeyAccountabilitiesSectionProfileViewModelFromJSON(json['keyAccountabilities']),
        'communicationAndWorkingRelationship': json['communicationAndWorkingRelationship'] == null ? undefined : RelationshipSectionProfileViewModelFromJSON(json['communicationAndWorkingRelationship']),
        'workQualification': json['workQualification'] == null ? undefined : WorkQualificationSectionProfileViewModelFromJSON(json['workQualification']),
        'workCondition': json['workCondition'] == null ? undefined : WorkConditionSectionProfileViewModelFromJSON(json['workCondition']),
        'kpi': json['kpi'] == null ? undefined : KpiSectionProfileViewModelFromJSON(json['kpi']),
    };
}

export function JobDescriptionUnifiedProfileViewModelToJSON(json: any): JobDescriptionUnifiedProfileViewModel {
    return JobDescriptionUnifiedProfileViewModelToJSONTyped(json, false);
}

export function JobDescriptionUnifiedProfileViewModelToJSONTyped(value?: JobDescriptionUnifiedProfileViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'version': value['version'],
        'status': JobDescriptionStatusProfileViewModelToJSON(value['status']),
        'lastApprovedBy': value['lastApprovedBy'],
        'approvedDate': value['approvedDate'] == null ? undefined : ((value['approvedDate'] as any).toISOString()),
        'modifiedBy': value['modifiedBy'],
        'modifiedDate': value['modifiedDate'] == null ? undefined : ((value['modifiedDate'] as any).toISOString()),
        'odObjectInfo': OdObjectInfoProfileViewModelToJSON(value['odObjectInfo']),
        'details': DetailsSectionProfileViewModelToJSON(value['details']),
        'dimensions': DimensionsSectionProfileViewModelToJSON(value['dimensions']),
        'skillsManagement': SkillsManagementUnifiedSectionProfileViewModelToJSON(value['skillsManagement']),
        'purpose': PurposeSectionProfileViewModelToJSON(value['purpose']),
        'keyAccountabilities': KeyAccountabilitiesSectionProfileViewModelToJSON(value['keyAccountabilities']),
        'communicationAndWorkingRelationship': RelationshipSectionProfileViewModelToJSON(value['communicationAndWorkingRelationship']),
        'workQualification': WorkQualificationSectionProfileViewModelToJSON(value['workQualification']),
        'workCondition': WorkConditionSectionProfileViewModelToJSON(value['workCondition']),
        'kpi': KpiSectionProfileViewModelToJSON(value['kpi']),
    };
}

