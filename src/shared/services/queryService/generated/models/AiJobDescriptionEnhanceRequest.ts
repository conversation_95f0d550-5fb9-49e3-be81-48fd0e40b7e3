/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface AiJobDescriptionEnhanceRequest
 */
export interface AiJobDescriptionEnhanceRequest {
    /**
     * 
     * @type {string}
     * @memberof AiJobDescriptionEnhanceRequest
     */
    text: string;
    /**
     * 
     * @type {string}
     * @memberof AiJobDescriptionEnhanceRequest
     */
    prompt: string;
}

/**
 * Check if a given object implements the AiJobDescriptionEnhanceRequest interface.
 */
export function instanceOfAiJobDescriptionEnhanceRequest(value: object): value is AiJobDescriptionEnhanceRequest {
    if (!('text' in value) || value['text'] === undefined) return false;
    if (!('prompt' in value) || value['prompt'] === undefined) return false;
    return true;
}

export function AiJobDescriptionEnhanceRequestFromJSON(json: any): AiJobDescriptionEnhanceRequest {
    return AiJobDescriptionEnhanceRequestFromJSONTyped(json, false);
}

export function AiJobDescriptionEnhanceRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): AiJobDescriptionEnhanceRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'text': json['text'],
        'prompt': json['prompt'],
    };
}

export function AiJobDescriptionEnhanceRequestToJSON(json: any): AiJobDescriptionEnhanceRequest {
    return AiJobDescriptionEnhanceRequestToJSONTyped(json, false);
}

export function AiJobDescriptionEnhanceRequestToJSONTyped(value?: AiJobDescriptionEnhanceRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'text': value['text'],
        'prompt': value['prompt'],
    };
}

