/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AppliedToPosition } from './AppliedToPosition';
import {
    AppliedToPositionFromJSON,
    AppliedToPositionFromJSONTyped,
    AppliedToPositionToJSON,
    AppliedToPositionToJSONTyped,
} from './AppliedToPosition';

/**
 * 
 * @export
 * @interface ActivateJobDescriptionResult
 */
export interface ActivateJobDescriptionResult {
    /**
     * 
     * @type {boolean}
     * @memberof ActivateJobDescriptionResult
     */
    isSuccess: boolean;
    /**
     * 
     * @type {Array<AppliedToPosition>}
     * @memberof ActivateJobDescriptionResult
     */
    appliedToPositions?: Array<AppliedToPosition> | null;
    /**
     * 
     * @type {Array<AppliedToPosition>}
     * @memberof ActivateJobDescriptionResult
     */
    skippedPositions?: Array<AppliedToPosition> | null;
}

/**
 * Check if a given object implements the ActivateJobDescriptionResult interface.
 */
export function instanceOfActivateJobDescriptionResult(value: object): value is ActivateJobDescriptionResult {
    if (!('isSuccess' in value) || value['isSuccess'] === undefined) return false;
    return true;
}

export function ActivateJobDescriptionResultFromJSON(json: any): ActivateJobDescriptionResult {
    return ActivateJobDescriptionResultFromJSONTyped(json, false);
}

export function ActivateJobDescriptionResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActivateJobDescriptionResult {
    if (json == null) {
        return json;
    }
    return {
        
        'isSuccess': json['isSuccess'],
        'appliedToPositions': json['appliedToPositions'] == null ? undefined : ((json['appliedToPositions'] as Array<any>).map(AppliedToPositionFromJSON)),
        'skippedPositions': json['skippedPositions'] == null ? undefined : ((json['skippedPositions'] as Array<any>).map(AppliedToPositionFromJSON)),
    };
}

export function ActivateJobDescriptionResultToJSON(json: any): ActivateJobDescriptionResult {
    return ActivateJobDescriptionResultToJSONTyped(json, false);
}

export function ActivateJobDescriptionResultToJSONTyped(value?: ActivateJobDescriptionResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'isSuccess': value['isSuccess'],
        'appliedToPositions': value['appliedToPositions'] == null ? undefined : ((value['appliedToPositions'] as Array<any>).map(AppliedToPositionToJSON)),
        'skippedPositions': value['skippedPositions'] == null ? undefined : ((value['skippedPositions'] as Array<any>).map(AppliedToPositionToJSON)),
    };
}

