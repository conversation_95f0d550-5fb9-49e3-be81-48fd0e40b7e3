/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PrePopulationRuleViewModel
 */
export interface PrePopulationRuleViewModel {
    /**
     * 
     * @type {string}
     * @memberof PrePopulationRuleViewModel
     */
    id: string;
    /**
     * 
     * @type {number}
     * @memberof PrePopulationRuleViewModel
     */
    number: number;
    /**
     * 
     * @type {string}
     * @memberof PrePopulationRuleViewModel
     */
    company: string;
    /**
     * 
     * @type {string}
     * @memberof PrePopulationRuleViewModel
     */
    positionLevel?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PrePopulationRuleViewModel
     */
    positionName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PrePopulationRuleViewModel
     */
    defaultContent: string;
    /**
     * 
     * @type {Date}
     * @memberof PrePopulationRuleViewModel
     */
    validFrom?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof PrePopulationRuleViewModel
     */
    validTo?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof PrePopulationRuleViewModel
     */
    isActive: boolean;
    /**
     * 
     * @type {string}
     * @memberof PrePopulationRuleViewModel
     */
    field: string;
}

/**
 * Check if a given object implements the PrePopulationRuleViewModel interface.
 */
export function instanceOfPrePopulationRuleViewModel(value: object): value is PrePopulationRuleViewModel {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('number' in value) || value['number'] === undefined) return false;
    if (!('company' in value) || value['company'] === undefined) return false;
    if (!('defaultContent' in value) || value['defaultContent'] === undefined) return false;
    if (!('isActive' in value) || value['isActive'] === undefined) return false;
    if (!('field' in value) || value['field'] === undefined) return false;
    return true;
}

export function PrePopulationRuleViewModelFromJSON(json: any): PrePopulationRuleViewModel {
    return PrePopulationRuleViewModelFromJSONTyped(json, false);
}

export function PrePopulationRuleViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): PrePopulationRuleViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'number': json['number'],
        'company': json['company'],
        'positionLevel': json['positionLevel'] == null ? undefined : json['positionLevel'],
        'positionName': json['positionName'] == null ? undefined : json['positionName'],
        'defaultContent': json['defaultContent'],
        'validFrom': json['validFrom'] == null ? undefined : (new Date(json['validFrom'])),
        'validTo': json['validTo'] == null ? undefined : (new Date(json['validTo'])),
        'isActive': json['isActive'],
        'field': json['field'],
    };
}

export function PrePopulationRuleViewModelToJSON(json: any): PrePopulationRuleViewModel {
    return PrePopulationRuleViewModelToJSONTyped(json, false);
}

export function PrePopulationRuleViewModelToJSONTyped(value?: PrePopulationRuleViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'number': value['number'],
        'company': value['company'],
        'positionLevel': value['positionLevel'],
        'positionName': value['positionName'],
        'defaultContent': value['defaultContent'],
        'validFrom': value['validFrom'] == null ? undefined : ((value['validFrom'] as any).toISOString()),
        'validTo': value['validTo'] == null ? undefined : ((value['validTo'] as any).toISOString()),
        'isActive': value['isActive'],
        'field': value['field'],
    };
}

