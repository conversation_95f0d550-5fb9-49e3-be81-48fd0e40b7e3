/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SubDisciplineJdViewModel } from './SubDisciplineJdViewModel';
import {
    SubDisciplineJdViewModelFromJSON,
    SubDisciplineJdViewModelFromJSONTyped,
    SubDisciplineJdViewModelToJSON,
    SubDisciplineJdViewModelToJSONTyped,
} from './SubDisciplineJdViewModel';

/**
 * 
 * @export
 * @interface DisciplineJdViewModel
 */
export interface DisciplineJdViewModel {
    /**
     * 
     * @type {string}
     * @memberof DisciplineJdViewModel
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof DisciplineJdViewModel
     */
    name: string;
    /**
     * 
     * @type {Array<SubDisciplineJdViewModel>}
     * @memberof DisciplineJdViewModel
     */
    subDisciplines?: Array<SubDisciplineJdViewModel> | null;
}

/**
 * Check if a given object implements the DisciplineJdViewModel interface.
 */
export function instanceOfDisciplineJdViewModel(value: object): value is DisciplineJdViewModel {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function DisciplineJdViewModelFromJSON(json: any): DisciplineJdViewModel {
    return DisciplineJdViewModelFromJSONTyped(json, false);
}

export function DisciplineJdViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): DisciplineJdViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
        'subDisciplines': json['subDisciplines'] == null ? undefined : ((json['subDisciplines'] as Array<any>).map(SubDisciplineJdViewModelFromJSON)),
    };
}

export function DisciplineJdViewModelToJSON(json: any): DisciplineJdViewModel {
    return DisciplineJdViewModelToJSONTyped(json, false);
}

export function DisciplineJdViewModelToJSONTyped(value?: DisciplineJdViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'subDisciplines': value['subDisciplines'] == null ? undefined : ((value['subDisciplines'] as Array<any>).map(SubDisciplineJdViewModelToJSON)),
    };
}

