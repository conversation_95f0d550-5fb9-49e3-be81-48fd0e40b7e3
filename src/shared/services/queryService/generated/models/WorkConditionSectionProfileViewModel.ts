/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface WorkConditionSectionProfileViewModel
 */
export interface WorkConditionSectionProfileViewModel {
    /**
     * 
     * @type {string}
     * @memberof WorkConditionSectionProfileViewModel
     */
    id?: string | null;
    /**
     * 
     * @type {string}
     * @memberof WorkConditionSectionProfileViewModel
     */
    physicalEffort?: string | null;
    /**
     * 
     * @type {string}
     * @memberof WorkConditionSectionProfileViewModel
     */
    workEnvironment?: string | null;
}

/**
 * Check if a given object implements the WorkConditionSectionProfileViewModel interface.
 */
export function instanceOfWorkConditionSectionProfileViewModel(value: object): value is WorkConditionSectionProfileViewModel {
    return true;
}

export function WorkConditionSectionProfileViewModelFromJSON(json: any): WorkConditionSectionProfileViewModel {
    return WorkConditionSectionProfileViewModelFromJSONTyped(json, false);
}

export function WorkConditionSectionProfileViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): WorkConditionSectionProfileViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'physicalEffort': json['physicalEffort'] == null ? undefined : json['physicalEffort'],
        'workEnvironment': json['workEnvironment'] == null ? undefined : json['workEnvironment'],
    };
}

export function WorkConditionSectionProfileViewModelToJSON(json: any): WorkConditionSectionProfileViewModel {
    return WorkConditionSectionProfileViewModelToJSONTyped(json, false);
}

export function WorkConditionSectionProfileViewModelToJSONTyped(value?: WorkConditionSectionProfileViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'physicalEffort': value['physicalEffort'],
        'workEnvironment': value['workEnvironment'],
    };
}

