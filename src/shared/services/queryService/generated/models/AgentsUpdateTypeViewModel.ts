/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const AgentsUpdateTypeViewModel = {
    NUMBER_0: 0,
    NUMBER_1: 1,
    NUMBER_2: 2,
    NUMBER_3: 3
} as const;
export type AgentsUpdateTypeViewModel = typeof AgentsUpdateTypeViewModel[keyof typeof AgentsUpdateTypeViewModel];


export function instanceOfAgentsUpdateTypeViewModel(value: any): boolean {
    for (const key in AgentsUpdateTypeViewModel) {
        if (Object.prototype.hasOwnProperty.call(AgentsUpdateTypeViewModel, key)) {
            if (AgentsUpdateTypeViewModel[key as keyof typeof AgentsUpdateTypeViewModel] === value) {
                return true;
            }
        }
    }
    return false;
}

export function AgentsUpdateTypeViewModelFromJSON(json: any): AgentsUpdateTypeViewModel {
    return AgentsUpdateTypeViewModelFromJSONTyped(json, false);
}

export function AgentsUpdateTypeViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): AgentsUpdateTypeViewModel {
    return json as AgentsUpdateTypeViewModel;
}

export function AgentsUpdateTypeViewModelToJSON(value?: AgentsUpdateTypeViewModel | null): any {
    return value as any;
}

export function AgentsUpdateTypeViewModelToJSONTyped(value: any, ignoreDiscriminator: boolean): AgentsUpdateTypeViewModel {
    return value as AgentsUpdateTypeViewModel;
}

