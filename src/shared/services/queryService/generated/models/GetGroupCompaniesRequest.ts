/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SkillFiltersRequest } from './SkillFiltersRequest';
import {
    SkillFiltersRequestFromJSON,
    SkillFiltersRequestFromJSONTyped,
    SkillFiltersRequestToJSON,
    SkillFiltersRequestToJSONTyped,
} from './SkillFiltersRequest';

/**
 * 
 * @export
 * @interface GetGroupCompaniesRequest
 */
export interface GetGroupCompaniesRequest {
    /**
     * 
     * @type {string}
     * @memberof GetGroupCompaniesRequest
     */
    search?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetGroupCompaniesRequest
     */
    orderBy?: string | null;
    /**
     * 
     * @type {SkillFiltersRequest}
     * @memberof GetGroupCompaniesRequest
     */
    skillFilters?: SkillFiltersRequest;
}

/**
 * Check if a given object implements the GetGroupCompaniesRequest interface.
 */
export function instanceOfGetGroupCompaniesRequest(value: object): value is GetGroupCompaniesRequest {
    return true;
}

export function GetGroupCompaniesRequestFromJSON(json: any): GetGroupCompaniesRequest {
    return GetGroupCompaniesRequestFromJSONTyped(json, false);
}

export function GetGroupCompaniesRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetGroupCompaniesRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'search': json['search'] == null ? undefined : json['search'],
        'orderBy': json['orderBy'] == null ? undefined : json['orderBy'],
        'skillFilters': json['skillFilters'] == null ? undefined : SkillFiltersRequestFromJSON(json['skillFilters']),
    };
}

export function GetGroupCompaniesRequestToJSON(json: any): GetGroupCompaniesRequest {
    return GetGroupCompaniesRequestToJSONTyped(json, false);
}

export function GetGroupCompaniesRequestToJSONTyped(value?: GetGroupCompaniesRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'search': value['search'],
        'orderBy': value['orderBy'],
        'skillFilters': SkillFiltersRequestToJSON(value['skillFilters']),
    };
}

