/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SkillGroupRequest
 */
export interface SkillGroupRequest {
    /**
     * 
     * @type {string}
     * @memberof SkillGroupRequest
     */
    name?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    companyIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    tagIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    libraryIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    typeIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    sourceIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    subFamilyIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SkillGroupRequest
     */
    subDisciplineIds?: Array<string> | null;
}

/**
 * Check if a given object implements the SkillGroupRequest interface.
 */
export function instanceOfSkillGroupRequest(value: object): value is SkillGroupRequest {
    return true;
}

export function SkillGroupRequestFromJSON(json: any): SkillGroupRequest {
    return SkillGroupRequestFromJSONTyped(json, false);
}

export function SkillGroupRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SkillGroupRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'] == null ? undefined : json['name'],
        'companyIds': json['companyIds'] == null ? undefined : json['companyIds'],
        'tagIds': json['tagIds'] == null ? undefined : json['tagIds'],
        'libraryIds': json['libraryIds'] == null ? undefined : json['libraryIds'],
        'typeIds': json['typeIds'] == null ? undefined : json['typeIds'],
        'sourceIds': json['sourceIds'] == null ? undefined : json['sourceIds'],
        'subFamilyIds': json['subFamilyIds'] == null ? undefined : json['subFamilyIds'],
        'subDisciplineIds': json['subDisciplineIds'] == null ? undefined : json['subDisciplineIds'],
    };
}

export function SkillGroupRequestToJSON(json: any): SkillGroupRequest {
    return SkillGroupRequestToJSONTyped(json, false);
}

export function SkillGroupRequestToJSONTyped(value?: SkillGroupRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'name': value['name'],
        'companyIds': value['companyIds'],
        'tagIds': value['tagIds'],
        'libraryIds': value['libraryIds'],
        'typeIds': value['typeIds'],
        'sourceIds': value['sourceIds'],
        'subFamilyIds': value['subFamilyIds'],
        'subDisciplineIds': value['subDisciplineIds'],
    };
}

