/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SubDisciplineViewModel } from './SubDisciplineViewModel';
import {
    SubDisciplineViewModelFromJSON,
    SubDisciplineViewModelFromJSONTyped,
    SubDisciplineViewModelToJSON,
    SubDisciplineViewModelToJSONTyped,
} from './SubDisciplineViewModel';

/**
 * 
 * @export
 * @interface DisciplineViewModel
 */
export interface DisciplineViewModel {
    /**
     * 
     * @type {string}
     * @memberof DisciplineViewModel
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof DisciplineViewModel
     */
    name: string;
    /**
     * 
     * @type {Array<SubDisciplineViewModel>}
     * @memberof DisciplineViewModel
     */
    subDisciplines?: Array<SubDisciplineViewModel> | null;
}

/**
 * Check if a given object implements the DisciplineViewModel interface.
 */
export function instanceOfDisciplineViewModel(value: object): value is DisciplineViewModel {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function DisciplineViewModelFromJSON(json: any): DisciplineViewModel {
    return DisciplineViewModelFromJSONTyped(json, false);
}

export function DisciplineViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): DisciplineViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
        'subDisciplines': json['subDisciplines'] == null ? undefined : ((json['subDisciplines'] as Array<any>).map(SubDisciplineViewModelFromJSON)),
    };
}

export function DisciplineViewModelToJSON(json: any): DisciplineViewModel {
    return DisciplineViewModelToJSONTyped(json, false);
}

export function DisciplineViewModelToJSONTyped(value?: DisciplineViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'subDisciplines': value['subDisciplines'] == null ? undefined : ((value['subDisciplines'] as Array<any>).map(SubDisciplineViewModelToJSON)),
    };
}

