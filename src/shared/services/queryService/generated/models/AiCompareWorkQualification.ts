/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AiCompareResult } from './AiCompareResult';
import {
    AiCompareResultFromJSON,
    AiCompareResultFromJSONTyped,
    AiCompareResultToJSON,
    AiCompareResultToJSONTyped,
} from './AiCompareResult';

/**
 * 
 * @export
 * @interface AiCompareWorkQualification
 */
export interface AiCompareWorkQualification {
    /**
     * 
     * @type {AiCompareResult}
     * @memberof AiCompareWorkQualification
     */
    minimumQualifications?: AiCompareResult;
    /**
     * 
     * @type {AiCompareResult}
     * @memberof AiCompareWorkQualification
     */
    minimumExperience?: AiCompareResult;
    /**
     * 
     * @type {AiCompareResult}
     * @memberof AiCompareWorkQualification
     */
    professionalCertificates?: AiCompareResult;
}

/**
 * Check if a given object implements the AiCompareWorkQualification interface.
 */
export function instanceOfAiCompareWorkQualification(value: object): value is AiCompareWorkQualification {
    return true;
}

export function AiCompareWorkQualificationFromJSON(json: any): AiCompareWorkQualification {
    return AiCompareWorkQualificationFromJSONTyped(json, false);
}

export function AiCompareWorkQualificationFromJSONTyped(json: any, ignoreDiscriminator: boolean): AiCompareWorkQualification {
    if (json == null) {
        return json;
    }
    return {
        
        'minimumQualifications': json['minimumQualifications'] == null ? undefined : AiCompareResultFromJSON(json['minimumQualifications']),
        'minimumExperience': json['minimumExperience'] == null ? undefined : AiCompareResultFromJSON(json['minimumExperience']),
        'professionalCertificates': json['professionalCertificates'] == null ? undefined : AiCompareResultFromJSON(json['professionalCertificates']),
    };
}

export function AiCompareWorkQualificationToJSON(json: any): AiCompareWorkQualification {
    return AiCompareWorkQualificationToJSONTyped(json, false);
}

export function AiCompareWorkQualificationToJSONTyped(value?: AiCompareWorkQualification | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'minimumQualifications': AiCompareResultToJSON(value['minimumQualifications']),
        'minimumExperience': AiCompareResultToJSON(value['minimumExperience']),
        'professionalCertificates': AiCompareResultToJSON(value['professionalCertificates']),
    };
}

