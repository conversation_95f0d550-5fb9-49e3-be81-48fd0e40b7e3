/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CommunicationAndWorkingRelationshipSectionViewModel
 */
export interface CommunicationAndWorkingRelationshipSectionViewModel {
    /**
     * 
     * @type {string}
     * @memberof CommunicationAndWorkingRelationshipSectionViewModel
     */
    internal?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommunicationAndWorkingRelationshipSectionViewModel
     */
    external?: string | null;
}

/**
 * Check if a given object implements the CommunicationAndWorkingRelationshipSectionViewModel interface.
 */
export function instanceOfCommunicationAndWorkingRelationshipSectionViewModel(value: object): value is CommunicationAndWorkingRelationshipSectionViewModel {
    return true;
}

export function CommunicationAndWorkingRelationshipSectionViewModelFromJSON(json: any): CommunicationAndWorkingRelationshipSectionViewModel {
    return CommunicationAndWorkingRelationshipSectionViewModelFromJSONTyped(json, false);
}

export function CommunicationAndWorkingRelationshipSectionViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): CommunicationAndWorkingRelationshipSectionViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'internal': json['internal'] == null ? undefined : json['internal'],
        'external': json['external'] == null ? undefined : json['external'],
    };
}

export function CommunicationAndWorkingRelationshipSectionViewModelToJSON(json: any): CommunicationAndWorkingRelationshipSectionViewModel {
    return CommunicationAndWorkingRelationshipSectionViewModelToJSONTyped(json, false);
}

export function CommunicationAndWorkingRelationshipSectionViewModelToJSONTyped(value?: CommunicationAndWorkingRelationshipSectionViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'internal': value['internal'],
        'external': value['external'],
    };
}

