import { createFeatureFlagsManager } from '@ot/feature-flags';

export const enum AdminFeatureFlag {
  AdminAtrAuditLog = 'Admin.ATR.AuditLog',
  AdminAtrInsights = 'Admin.ATR.Insights'

  // TODO: add feature flag for general
  // AdminGeneralFeature = 'Admin.General.Feature'
}

export type AdminFeatureFlags = `${AdminFeatureFlag}`;

export const {
  FeatureFlagsProvider,
  FeatureFlag,
  useFeatureFlag,
  withFeatureFlag
} = createFeatureFlagsManager<AdminFeatureFlags>();
