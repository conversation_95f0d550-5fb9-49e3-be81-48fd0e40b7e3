import { SortingOption } from '@epam/uui-core';

import { isSortingChanged } from '../utils';

interface DataItem {
  name: string;
  id: string;
}

describe('isSortingChanged', () => {
  it.each([
    [undefined, undefined, false],
    [undefined, [], false],
    [[], undefined, false],
    [[{ field: 'name' }], undefined, true],
    [[{ field: 'name' }], [], true],
    [undefined, [{ field: 'name' }], true],
    [[], [{ field: 'name' }], true],
    [[{ field: 'id' }], [{ field: 'name' }], true],
    [[{ field: 'id' }], [{ field: 'id', direction: 'desc' }], true],
    [
      [{ field: 'id', direction: 'asc' }],
      [{ field: 'id', direction: 'desc' }],
      true
    ],
    [[{ field: 'id' }], [{ field: 'id', direction: 'asc' }], false]
  ])(
    'when the prev is %o and the next is %o should return %s',
    (
      prev: { field: string; direction?: string }[] | undefined,
      next: { field: string; direction?: string }[] | undefined,
      result: boolean
    ) => {
      const actualResult = isSortingChanged<DataItem>(
        prev as unknown as SortingOption<DataItem>[],
        next as unknown as SortingOption<DataItem>[]
      );

      expect(actualResult).toBe(result);
    }
  );
});
