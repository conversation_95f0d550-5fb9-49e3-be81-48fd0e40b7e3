import React from 'react';

import { render, screen, fireEvent } from '@testing-library/react';

import '@testing-library/jest-dom';
import { IconButton, Tooltip } from '@oh/components';

import { TableAction } from '../actions/TableAction';

jest.mock('@oh/components', () => ({
  IconButton: jest.fn(({ onClick, children, ...props }) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  )),
  Tooltip: jest.fn(({ children }) => <div>{children}</div>)
}));

describe('TableAction Component', () => {
  const MockIcon = () => <svg />;

  it('renders IconButton with correct props and icon', () => {
    const onClick = jest.fn();
    render(
      <TableAction
        icon={MockIcon}
        onClick={onClick}
        disabled={false}
        id="test-id"
      />
    );
    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveAttribute('id', 'test-id');
    expect(IconButton).toHaveBeenCalledWith(
      expect.objectContaining({
        disabled: false,
        icon: MockIcon,
        id: 'test-id'
      }),
      expect.anything()
    );
  });

  it('does not call onClick when disabled is true', () => {
    const onClick = jest.fn();
    render(<TableAction icon={MockIcon} onClick={onClick} disabled={true} />);
    fireEvent.click(screen.getByRole('button'));
    expect(onClick).not.toHaveBeenCalled();
  });

  it('renders Tooltip with disabledMessage when disabled', () => {
    const disabledMessage = 'Cannot perform action';
    render(
      <TableAction
        icon={MockIcon}
        disabled={true}
        disabledMessage={disabledMessage}
      />
    );

    expect(Tooltip).toHaveBeenCalledWith(
      expect.objectContaining({
        title: disabledMessage
      }),
      expect.anything()
    );
  });

  it('does not render Tooltip with a title if disabled is false', () => {
    render(
      <TableAction
        icon={MockIcon}
        disabled={false}
        disabledMessage="Should not show"
      />
    );
    expect(Tooltip).toHaveBeenCalledWith(
      expect.objectContaining({
        title: undefined
      }),
      expect.anything()
    );
  });
});
