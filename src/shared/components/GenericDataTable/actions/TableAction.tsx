import { FC, MouseEvent, SVGProps, useCallback } from 'react';

import { IconButton, Tooltip } from '@oh/components';
import { TooltipVariant } from '@oh/constants';

export interface TableActionProps {
  disabled?: boolean;
  disabledMessage?: string;
  title?: string;
  onClick?: () => void;
  id?: string;
}

export interface TableActionComponentProps extends TableActionProps {
  icon: FC<SVGProps<SVGSVGElement>>;
  id?: string;
}

export const TableAction: FC<TableActionComponentProps> = ({
  icon,
  disabled,
  disabledMessage,
  title,
  onClick,
  id = ''
}) => {
  const onButtonClick = useCallback(
    (e: MouseEvent<HTMLButtonElement>) => {
      if (!onClick) {
        return;
      }

      onClick();
      e.stopPropagation();
    },
    [onClick]
  );
  return (
    <Tooltip
      variant={TooltipVariant.Dark}
      title={disabled && disabledMessage ? disabledMessage : undefined}
    >
      <IconButton
        disabled={disabled}
        title={title}
        icon={icon}
        className="!bg-transparent !text-link-rested hover:text-link-hover active:!bg-transparent active:!text-link-pressed disabled:!text-link-disabled active:disabled:!text-link-disabled"
        iconClassName="!size-20 !opacity-100"
        onClick={onButtonClick}
        id={id}
      />
    </Tooltip>
  );
};
