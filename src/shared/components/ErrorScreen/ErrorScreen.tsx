import { FC } from 'react';

import { IllustrationMessage, IllustrationVariant } from '@ot/onetalent-ui-kit';
import clsx from 'clsx';

import { QueryService } from '@/shared/services';

import { DEFAULT_ERROR_MESSAGE, ERROR_MESSAGES } from './constants';

import { hasResponseError } from '../../core/domainModel/api';
import { useTraceID } from '../../hooks/useTraceID';

interface ErrorScreenProps {
  illustrationVariant?: IllustrationVariant;
  contentClassName?: string;
  buttonClassName?: string;
  iconClassName?: string;
  title?: string;
  subtitle?: string;
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  descriptionClassName?: string;
  error?: unknown;
}

const getStatusCode = (
  error: Error | QueryService.ResponseError
): number | undefined => {
  if (typeof error === 'number') return error;
  if (error && hasResponseError(error)) {
    return error.response.status;
  }
  return undefined;
};

export const getErrorMessages = (statusCode?: number) => {
  if (!statusCode) return DEFAULT_ERROR_MESSAGE;
  return ERROR_MESSAGES[statusCode] ?? DEFAULT_ERROR_MESSAGE;
};

export const ErrorScreen: FC<ErrorScreenProps> = ({
  className,
  title = DEFAULT_ERROR_MESSAGE.title,
  subtitle = DEFAULT_ERROR_MESSAGE.subtitle,
  titleClassName,
  subtitleClassName,
  descriptionClassName,
  illustrationVariant = IllustrationVariant.Error,
  error
}) => {
  const traceID = useTraceID();
  const statusCode = getStatusCode(error as Error);
  const messages = getErrorMessages(statusCode);

  // Notes:
  // - Phase 1: Display the "Something Went Wrong" message on Skills/JD, Objectives / Talent connect and Talent Journey pages (low risk)
  // - Phase 2: Extend the message to ATR and Feedback pages (low risk)

  return (
    <div
      data-attributes="ErrorScreen"
      className="error-screen flex h-full w-full items-center justify-center"
    >
      <IllustrationMessage
        illustrationVariant={illustrationVariant ?? IllustrationVariant.Error}
        title={title ?? messages.title}
        subtitle={subtitle ?? messages.subtitle}
        {...(traceID ? { description: `Trace ID: ${traceID}` } : {})}
        className={clsx('max-w-none', className)}
        titleClassName={titleClassName}
        subtitleClassName={subtitleClassName}
        descriptionClassName={descriptionClassName}
      />
    </div>
  );
};
