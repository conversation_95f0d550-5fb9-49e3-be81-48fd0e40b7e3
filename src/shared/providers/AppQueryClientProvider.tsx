import { FC, PropsWithChildren, useState } from 'react';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

import { checkIsLocalDevelopment } from '@oh/utils';

const createQueryClientInstance = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: 0
      }
    }
  });

export const AppQueryClientProvider: FC<PropsWithChildren> = ({ children }) => {
  const [queryClient] = useState(createQueryClientInstance());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {checkIsLocalDevelopment() && <ReactQueryDevtools />}
    </QueryClientProvider>
  );
};
