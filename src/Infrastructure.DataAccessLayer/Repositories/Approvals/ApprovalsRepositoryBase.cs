using Microsoft.EntityFrameworkCore;
using OneTalent.Common.Extensions.Exceptions;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Approvals.Commands.Models;
using OneTalent.FeedbackService.Application.Approvals.Queries.Repositories;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer.Entities.Feedback;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer.Repositories.Feedback;
using System.Diagnostics;

namespace OneTalent.FeedbackService.Infrastructure.DataAccessLayer.Repositories.Approvals;

internal abstract class ApprovalsRepositoryBase(FeedbackDbContext dbContext) : IApprovalsReadRepository
{
    protected readonly FeedbackDbContext DbContext = dbContext;

    protected abstract IQueryable<FeedbackToApprovalsEntity> GetFeedbackToApprovalsQuery();

    public async Task<Application.Feedback.Queries.Models.Feedback> GetFeedbackByApprovalIdAsync(string approvalId, ItemId employeeId, CancellationToken cancellationToken)
    {
        var feedback = await GetFeedbackToApprovalsQuery()
            .Join(
                DbContext.Feedback,
                x => x.FeedbackId,
                x => x.Id,
                (a, f) => new { a.ApprovalId, Feedback = f })
            .Where(x => x.ApprovalId == approvalId && (x.Feedback.RequestorId == employeeId || x.Feedback.GiverId == employeeId || x.Feedback.ReceiverId == employeeId))
            .Select(x => x.Feedback)
            .FirstOrDefaultAsync(cancellationToken)
                 ?? throw new ItemNotFoundException($"Feedback with approval ID {approvalId} wasn't found.");

        return feedback.ToFeedback();
    }

    public async Task<FeedbackToApproval?> GetByFeedbackIdAsync(ItemId feedbackId, CancellationToken cancellationToken)
    {
        var entity = await GetFeedbackToApprovalsQuery()
            .FirstOrDefaultAsync(x => x.FeedbackId == feedbackId, cancellationToken);

        return entity?.ToFeedbackToApproval();
    }

    public async Task<FeedbackToApproval> GetByApprovalIdAsync(string approvalId, CancellationToken cancellationToken)
    {
        var entity = await GetFeedbackToApprovalsQuery()
                .FirstOrDefaultAsync(x => x.ApprovalId == approvalId, cancellationToken)
            ?? throw new ItemNotFoundException($"Feedback with approval ID {approvalId} wasn't found.");

        return entity.ToFeedbackToApproval();
    }
}
