using Microsoft.EntityFrameworkCore;
using OneTalent.FeedbackService.Application.Common;
using OneTalent.FeedbackService.Application.Common.Exceptions;

namespace OneTalent.FeedbackService.Infrastructure.DataAccessLayer;

internal sealed class UnitOfWork(FeedbackDbContext dbContext) : IUnitOfWork
{
    public async Task<bool> ExecuteInTransactionAsync(Func<CancellationToken, Task> func, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(func, nameof(func));

        if (dbContext.Database.CurrentTransaction is not null)
        {
            await func(cancellationToken);

            return false;
        }

        await dbContext.Database.CreateExecutionStrategy().ExecuteAsync(async () =>
        {
            await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);
            await func(cancellationToken);
            await transaction.CommitAsync(cancellationToken);
        });

        return true;
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken)
    {
        try
        {
            return await dbContext.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateConcurrencyException exception)
        {
            dbContext.ChangeTracker.Clear();
            throw new ConcurrencyException("Concurrency Exception.", exception);
        }
    }
}
