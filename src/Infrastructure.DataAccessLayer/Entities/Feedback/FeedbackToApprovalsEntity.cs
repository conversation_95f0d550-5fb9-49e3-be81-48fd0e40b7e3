using OneTalent.Common.Extensions.ItemId;
using System.ComponentModel.DataAnnotations;

namespace OneTalent.FeedbackService.Infrastructure.DataAccessLayer.Entities.Feedback;

public record FeedbackToApprovalsEntity(
    ItemId FeedbackId,
    string ApprovalId,
    Guid CompletedOperationId
) : IHasFeedbackFk
{
    public Guid CompletedOperationId { get; set; } = CompletedOperationId;

    [Timestamp]
    public byte[] Version { get; private set; } = null!;
}
