import { FC } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>V<PERSON><PERSON>, Informer, InformerVariant } from '@/components';

interface FailToLoadObjectivesInformerProps {
  tryAgain(): void;
}

export const FailToLoadObjectivesInformer: FC<
  FailToLoadObjectivesInformerProps
> = ({ tryAgain }) => (
  <Informer
    variant={InformerVariant.Error}
    title="We couldn't load the list of tasks and milestones due to an error"
    description={
      <>
        Please&nbsp;
        <Button
          className="inline-flex"
          variant={ButtonVariant.Link}
          onClick={tryAgain}
        >
          Try Again
        </Button>
        , check back soon or contact support if the issue persists
      </>
    }
  />
);
