import { Link } from 'react-router-dom';

import {
  Button,
  ButtonSize,
  ButtonVarian<PERSON>,
  Chip,
  ModalFooter
} from '@ot/onetalent-ui-kit';

import { useBreakpointsContext } from '@/contexts';
import { FeedbackHeader } from '@/feedback/components/FeedbackHeader';
import { FeedbackRoute } from '@/feedback/constants';
import { useFeedbackType } from '@/feedback/context';
import { useFeedbackOverview } from '@/feedback/domain/employees/hooks/useFeedbackOverview';
import {
  isFeedbackTypeGiven,
  isFeedbackTypeReceived,
  isFeedbackTypeRequested
} from '@/feedback/utils';

export const MyFeedbackHeader = () => {
  const { type } = useFeedbackType();
  const { data } = useFeedbackOverview();
  const { isMobile } = useBreakpointsContext();

  return (
    <>
      <FeedbackHeader title="My Feedback" containerClassName="py-16">
        {!isMobile && (
          <>
            <Link to={FeedbackRoute.MyFeedbackGive}>
              <Button
                size={ButtonSize.Medium}
                variant={ButtonVariant.Secondary}
              >
                Give Feedback
              </Button>
            </Link>
            <Link to={FeedbackRoute.MyFeedbackRequest} className="ml-20">
              <Button size={ButtonSize.Medium} variant={ButtonVariant.Primary}>
                Request Feedback
              </Button>
            </Link>
          </>
        )}
      </FeedbackHeader>

      <div className="scrollbar-hidden mb-16 flex items-baseline justify-items-start gap-12 overflow-x-auto">
        <Link to={FeedbackRoute.MyFeedbackReceived}>
          <Chip selected={isFeedbackTypeReceived(type)}>
            Received {`(${data?.receivedCount || 0})`}
          </Chip>
        </Link>
        <Link to={FeedbackRoute.MyFeedbackGiven}>
          <Chip selected={isFeedbackTypeGiven(type)}>
            Given {`(${data?.givenCount || 0})`}
          </Chip>
        </Link>
        <Link to={FeedbackRoute.MyFeedbackRequested}>
          <Chip selected={isFeedbackTypeRequested(type)}>
            Requests History {`(${data?.requestedCount || 0})`}
          </Chip>
        </Link>
      </div>
      {isMobile && (
        <ModalFooter className="fixed bottom-0 left-0 right-0 z-[1030]">
          <Link to={FeedbackRoute.MyFeedbackGive}>
            <Button size={ButtonSize.Medium} variant={ButtonVariant.Secondary}>
              Give Feedback
            </Button>
          </Link>
          <Link to={FeedbackRoute.MyFeedbackRequest}>
            <Button size={ButtonSize.Medium} variant={ButtonVariant.Primary}>
              Request Feedback
            </Button>
          </Link>
        </ModalFooter>
      )}
    </>
  );
};
