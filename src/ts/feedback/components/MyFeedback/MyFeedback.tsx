import { FC, ReactElement } from 'react';

import { useBreakpointsContext } from '@/contexts';
import { MyFeedbackHeader } from '@/feedback/components/MyFeedback/MyFeedbackHeader';
import { MyFeedbackList } from '@/feedback/components/MyFeedback/MyFeedbackList';
import { MyFeedbackTable } from '@/feedback/components/MyFeedback/MyFeedbackTable';

export const MyFeedback: FC = (): ReactElement => {
  const { isMobile } = useBreakpointsContext();

  return (
    <>
      <MyFeedbackHeader />
      {isMobile ? <MyFeedbackList /> : <MyFeedbackTable />}
    </>
  );
};
