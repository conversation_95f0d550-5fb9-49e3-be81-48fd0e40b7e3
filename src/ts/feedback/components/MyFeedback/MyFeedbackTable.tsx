import { FC, useCallback, useState } from 'react';

import { PerformanceDataTable, useDataSourceState } from '@/components';
import { FeedbackShortDto } from '@/feedback/api/generated';
import { MyFeedbackEmpty } from '@/feedback/components/MyFeedback/MyFeedbackEmpty';
import {
  DEFAULT_PAGE_SIZE,
  DEFAULT_VISIBLE_COUNT,
  FEEDBACK_MIN_HEIGHT_TABLE
} from '@/feedback/constants';
import { useFeedbackType } from '@/feedback/context';
// import { useCancelFeedback } from '@/feedback/domain/requesters/hooks/useCancelFeedback';
import { useFeedbackTableColumns, useMyFeedback } from '@/feedback/hooks';
import { getDrawerNameByFeedbackType } from '@/feedback/utils';
import { useToggleVisibility } from '@/hooks';

import { FeedbackCancelConfirmationPopup } from '../FeedbackDetails/FeedbackCancelConfirmationPopup';

export interface MyFeedbackTableProps {
  emptyScreenSubtitle?: string;
  employeeId?: string;
}

export const MyFeedbackTable: FC<MyFeedbackTableProps> = ({ employeeId }) => {
  const { toggleDrawer } = useToggleVisibility();
  const [cancelConfirmPopupIsOpen, setCancelConfirmPopupIsOpen] =
    useState(false);
  const [cancelFeedbackId, setCancelFeedbackId] = useState('');

  const handleCancelConfirmationOpen = useCallback(
    (ctx: FeedbackShortDto | undefined) => {
      setCancelFeedbackId(ctx?.id ?? '');
      setCancelConfirmPopupIsOpen(true);
    },
    []
  );

  const handleCancelConfirmationClose = useCallback(() => {
    setCancelFeedbackId('');
    setCancelConfirmPopupIsOpen(false);
  }, []);

  const { type } = useFeedbackType();
  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize: DEFAULT_PAGE_SIZE,
    visibleCount: DEFAULT_VISIBLE_COUNT
  });
  const queryResult = useMyFeedback(type, {
    pageSize: pagination.pageSize,
    pageNumber: pagination.pageNumber,
    receiverId: employeeId
  });
  const columns = useFeedbackTableColumns(type, handleCancelConfirmationOpen);

  const onRowClick = useCallback(
    (feedbackId: string) => {
      toggleDrawer({ name: getDrawerNameByFeedbackType({ type }), feedbackId });
    },
    [toggleDrawer, type]
  );

  return (
    <>
      <PerformanceDataTable
        dataAttributes="feedbackTable"
        {...dataSource}
        showContainer
        styles={{
          minHeight: !queryResult.data?.items.length
            ? FEEDBACK_MIN_HEIGHT_TABLE
            : 0
        }}
        queryResult={queryResult}
        columns={columns}
        renderNoResults={MyFeedbackEmpty}
        onRowClick={onRowClick}
        paginationSize="24"
      />
      <FeedbackCancelConfirmationPopup
        isOpen={cancelConfirmPopupIsOpen}
        onClose={handleCancelConfirmationClose}
        cancelFeedbackId={cancelFeedbackId}
      />
    </>
  );
};
