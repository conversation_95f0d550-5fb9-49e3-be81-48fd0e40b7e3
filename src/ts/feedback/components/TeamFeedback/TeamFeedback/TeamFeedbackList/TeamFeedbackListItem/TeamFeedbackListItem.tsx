import { FC, useState } from 'react';

import { Drawer } from '@ot/onetalent-ui-kit';
import clsx from 'clsx';

import { UserInfo, UserInfoSize } from '@/components';
import { AlignmentVertical } from '@/components/UserInfo/types';
import { EmployeeInfoDto } from '@/feedback/api/generated';
import { SubordinateWithFeedbackInfoDto } from '@/feedback/api/generated/models/SubordinateWithFeedbackInfoDto';
import { FeatureFlag } from '@/feedback/modules/featureFlags';
import { canViewSubordinateFeedbackDetails } from '@/feedback/utils/permissions';

interface MyFeedbackListItemProps {
  className?: string | null;
  user: SubordinateWithFeedbackInfoDto & Pick<EmployeeInfoDto, 'permissions'>;
  onClick(id: string): void;
}

export const TeamFeedbackListItem: FC<MyFeedbackListItemProps> = ({
  className,
  user,
  onClick
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const isEmployeeHasPermission = canViewSubordinateFeedbackDetails(
    user && user.permissions
  );

  return (
    <>
      <div
        data-attributes="MyFeedbackListItem"
        className={clsx(
          'flex cursor-pointer flex-col gap-16 rounded-xl bg-surface-grey_0 p-16 shadow-card',
          className
        )}
        onClick={() => {
          isEmployeeHasPermission
            ? onClick(user.employeeId)
            : setIsDrawerOpen(!isDrawerOpen);
        }}
      >
        <UserInfo
          email={user?.email}
          fullName={user?.fullNameEnglish}
          companyName={user?.companyName}
          positionName={user?.positionName}
          size={UserInfoSize.Medium}
          alignmentVertical={AlignmentVertical.Start}
          titleClassName={clsx(
            'text-label-m2-medium',
            isEmployeeHasPermission
              ? 'text-text-heading'
              : 'text-checkbox-text-disabled'
          )}
          subtitleClassName={clsx(
            'text-body-2-regular',
            isEmployeeHasPermission ? 'text-text-body' : 'text-text-secondary'
          )}
          isTitleTruncated={false}
          isSubtitleTruncated={false}
        />
        {isEmployeeHasPermission && (
          <div className="rounded-[12px] bg-surface-grey_10 p-12">
            <div className="flex w-[100%] justify-between">
              <span className="text-body-2-regular text-text-body">
                Total Feedback Received
              </span>
              <span className="text-body-2-medium text-text-heading">
                {user?.feedbackInfo?.receivedCount ?? 0}
              </span>
            </div>
            <FeatureFlag name="Feedback.MyTeamFeedback.Given">
              <div className="mt-12 flex w-[100%] justify-between">
                <span className="text-body-2-regular text-text-body">
                  Total Feedback Given
                </span>
                <span className="text-body-2-medium text-text-heading">
                  {user?.feedbackInfo?.givenCount ?? 0}
                </span>
              </div>
            </FeatureFlag>
          </div>
        )}
      </div>
      <Drawer
        dataAttributes="TeamFeedbackListItemDrawer"
        fullHeight={false}
        isOpen={isDrawerOpen}
        showClose
        variant="Primary"
      >
        <p className="text-body-2-regular">
          The employee does not have the necessary permissions to access the
          Feedback application
        </p>
      </Drawer>
    </>
  );
};
