import { FeedbackType } from '@/feedback/constants';
import {
  REQUESTERS_REQUEST_KEYS,
  useMyGivenFeedback,
  useMyReceivedFeedback,
  useMyRequestedFeedback
} from '@/feedback/domain';

export type GetQueryFunctionAndCacheKeyByFeedbackTypeParams = {
  type: FeedbackType;
  isMobile?: boolean;
};

export const useQueryFunctionAndCacheKeyByFeedbackType = ({
  type,
  isMobile = false
}: GetQueryFunctionAndCacheKeyByFeedbackTypeParams) => {
  switch (type) {
    case FeedbackType.Given:
      return {
        queryKey: isMobile
          ? REQUESTERS_REQUEST_KEYS.givenMobile
          : REQUESTERS_REQUEST_KEYS.given,
        useQueryFn: useMyGivenFeedback
      };
    case FeedbackType.Requested:
    case FeedbackType.TeamRequested:
      return {
        queryKey: isMobile
          ? REQUESTERS_REQUEST_KEYS.requestsHistoryMobile
          : REQUESTERS_REQUEST_KEYS.requestsHistory,
        useQueryFn: useMyRequestedFeedback
      };
    case FeedbackType.Received:
    default:
      return {
        queryKey: isMobile
          ? REQUESTERS_REQUEST_KEYS.receivedMobile
          : REQUESTERS_REQUEST_KEYS.received,
        useQueryFn: useMyReceivedFeedback
      };
  }
};
