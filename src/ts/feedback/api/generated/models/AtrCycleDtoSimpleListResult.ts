/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.FeedbackService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 4.0.272
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AtrCycleDto } from './AtrCycleDto';
import {
    AtrCycleDtoFromJSON,
    AtrCycleDtoFromJSONTyped,
    AtrCycleDtoToJSON,
} from './AtrCycleDto';

/**
 * 
 * @export
 * @interface AtrCycleDtoSimpleListResult
 */
export interface AtrCycleDtoSimpleListResult {
    /**
     * 
     * @type {Array<AtrCycleDto>}
     * @memberof AtrCycleDtoSimpleListResult
     */
    items: Array<AtrCycleDto>;
}

/**
 * Check if a given object implements the AtrCycleDtoSimpleListResult interface.
 */
export function instanceOfAtrCycleDtoSimpleListResult(value: object): value is AtrCycleDtoSimpleListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    return true;
}

export function AtrCycleDtoSimpleListResultFromJSON(json: any): AtrCycleDtoSimpleListResult {
    return AtrCycleDtoSimpleListResultFromJSONTyped(json, false);
}

export function AtrCycleDtoSimpleListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): AtrCycleDtoSimpleListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(AtrCycleDtoFromJSON)),
    };
}

export function AtrCycleDtoSimpleListResultToJSON(value?: AtrCycleDtoSimpleListResult | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'items': ((value['items'] as Array<any>).map(AtrCycleDtoToJSON)),
    };
}

