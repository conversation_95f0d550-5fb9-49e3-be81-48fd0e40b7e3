/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.FeedbackService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 4.0.272
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface WorkflowHistoryStepResponse
 */
export interface WorkflowHistoryStepResponse {
    /**
     * 
     * @type {number}
     * @memberof WorkflowHistoryStepResponse
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof WorkflowHistoryStepResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof WorkflowHistoryStepResponse
     */
    description: string;
}

/**
 * Check if a given object implements the WorkflowHistoryStepResponse interface.
 */
export function instanceOfWorkflowHistoryStepResponse(value: object): value is WorkflowHistoryStepResponse {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('description' in value) || value['description'] === undefined) return false;
    return true;
}

export function WorkflowHistoryStepResponseFromJSON(json: any): WorkflowHistoryStepResponse {
    return WorkflowHistoryStepResponseFromJSONTyped(json, false);
}

export function WorkflowHistoryStepResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): WorkflowHistoryStepResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
        'description': json['description'],
    };
}

export function WorkflowHistoryStepResponseToJSON(value?: WorkflowHistoryStepResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
        'description': value['description'],
    };
}

