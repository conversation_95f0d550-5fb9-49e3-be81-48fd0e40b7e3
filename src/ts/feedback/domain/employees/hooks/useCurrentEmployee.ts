import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { STALE_TIME } from '@/constants';
import { useFeedbackServiceApi } from '@/feedback/api';
import { logger } from '@/logger';

import { EMPLOYEES_REQUEST_KEYS } from '../employeesRequestKeys';

function useCurrentEmployeeRequest() {
  const { employeesApi } = useFeedbackServiceApi();

  return useCallback(async () => {
    try {
      return await employeesApi.v1EmployeesMeEmployeeInfoGet();
    } catch (error) {
      logger.error('Failed to load v1EmployeesMeEmployeeInfoGet', error);

      throw error;
    }
  }, [employeesApi]);
}

export function useCurrentEmployee() {
  const currentEmployeeRequestFn = useCurrentEmployeeRequest();
  return useQuery({
    queryKey: EMPLOYEES_REQUEST_KEYS.getCurrentEmployee(),
    queryFn: () => currentEmployeeRequestFn(),
    staleTime: STALE_TIME.SESSION
  });
}
