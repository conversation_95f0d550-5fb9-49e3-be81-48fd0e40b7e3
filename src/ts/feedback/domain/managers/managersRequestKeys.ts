import { FeedbackService } from '@/feedback/api';
import { GetSubordinatesFeedbackRequest } from '@/feedback/api/generated';

export const MANAGERS_REQUEST_KEYS = {
  all: () => ['managers'] as const,
  teamFeedback: (input: GetSubordinatesFeedbackRequest) =>
    [...MANAGERS_REQUEST_KEYS.all(), 'teamFeedback', input] as const,
  teamFeedbackMobile: () =>
    [...MANAGERS_REQUEST_KEYS.all(), 'teamFeedback', 'mobile'] as const,
  getSubordinateInfo: (employeeId: string) =>
    [...MANAGERS_REQUEST_KEYS.all(), 'getSubordinateInfo', employeeId] as const,
  subordinateFeedbackOverview: (subordinateId: string) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackOverview',
      subordinateId
    ] as const,
  subordinateFeedbackReceived: (
    subordinateId: string,
    params: FeedbackService.SearchMySubordinateReceivedFeedbackRequest = {}
  ) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackReceived',
      subordinateId,
      params
    ] as const,
  subordinateFeedbackReceivedMobile: (
    subordinateId: string,
    params: FeedbackService.SearchMySubordinateReceivedFeedbackRequest
  ) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackReceived',
      'mobile',
      subordinateId,
      params
    ] as const,
  subordinateFeedbackGiven: (
    subordinateId: string,
    params: FeedbackService.SearchMySubordinateGivenFeedbackRequest
  ) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackGiven',
      subordinateId,
      params
    ] as const,
  subordinateFeedbackGivenMobile: (
    subordinateId: string,
    params: FeedbackService.SearchMySubordinateGivenFeedbackRequest
  ) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackGiven',
      'mobile',
      subordinateId,
      params
    ] as const,
  subordinateFeedbackDetailsGiven: (
    input: FeedbackService.V1ManagersMeGiversSubordinateIdFeedbackIdGetRequest
  ) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackDetailsGiven',
      input
    ] as const,
  subordinateFeedbackDetailsReceived: (
    input: FeedbackService.V1ManagersMeReceiversSubordinateIdFeedbackIdGetRequest
  ) =>
    [
      ...MANAGERS_REQUEST_KEYS.all(),
      'subordinateFeedbackDetailsReceived',
      input
    ] as const,
  subordinates: (input: object = {}) => ['subordinates', input] as const,
  subordinateGivers: (
    input: FeedbackService.V1ManagersMeSubordinatesSubordinateIdGiversSearchPostRequest
  ) => ['subordinateGivers', input] as const,
  subordinateTopics: (
    input: FeedbackService.V1ManagersMeSubordinatesSubordinateIdTopicsGetRequest
  ) => ['subordinateTopics', input] as const
};
