import { useToast } from '@ot/onetalent-ui-kit';
import { logger } from '@services/logger';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useFeedbackServiceApi } from '@/feedback/api';
import { FeedbackType } from '@/feedback/constants';
import { MANAGERS_REQUEST_KEYS } from '@/feedback/domain/managers/managersRequestKeys';
import { FeedbackModel } from '@/feedback/domain/requesters/models/feedback';
import { REQUESTERS_REQUEST_KEYS } from '@/feedback/domain/requesters/requestersRequestKeys';
import { isSubordinateFeedbackTypeReceived } from '@/feedback/utils/isFeedbackType';

interface UpdateFeedbackVisibilityProps {
  feedbackId: string;
  isVisibleToReceiver: boolean;
  subordinateId: string;
}

export function useUpdateFeedbackVisibleForReports(type?: FeedbackType) {
  const { managersApi } = useFeedbackServiceApi();
  const { addToast } = useToast();
  const queryClient = useQueryClient();
  const isSubordinateFeedbackReceived = isSubordinateFeedbackTypeReceived(type);

  const { mutate: updateFeedbackVisibility, ...rest } = useMutation({
    async mutationFn({
      feedbackId,
      isVisibleToReceiver,
      subordinateId
    }: UpdateFeedbackVisibilityProps) {
      try {
        return await managersApi.v1ManagersMeSubordinatesSubordinateIdFeedbackIdPatch(
          {
            subordinateId: subordinateId,
            id: feedbackId,
            updateSubordinateFeedbackRequest: {
              isVisibleToReceiver
            }
          }
        );
      } catch (error) {
        logger.error(
          'v1ManagersMeSubordinatesSubordinateIdFeedbackIdPatch',
          error
        );

        throw error;
      }
    },
    onMutate: async ({
      feedbackId,
      isVisibleToReceiver,
      subordinateId
    }: UpdateFeedbackVisibilityProps) => {
      const key = isSubordinateFeedbackReceived
        ? MANAGERS_REQUEST_KEYS.subordinateFeedbackDetailsReceived({
            id: feedbackId,
            subordinateId
          })
        : REQUESTERS_REQUEST_KEYS.feedbackRequestedById({ id: feedbackId });

      queryClient.setQueryData(key, (old: FeedbackModel) => ({
        ...old,
        isVisibleToReceiver: isVisibleToReceiver
      }));

      return {
        isVisibleToReceiver: !isVisibleToReceiver,
        feedbackId,
        subordinateId
      };
    },
    onSuccess() {
      addToast({
        title: 'Feedback visibility was changed successfully',
        variant: 'Success'
      });
    },
    onError(error, _, context) {
      addToast({
        title: 'An error occurred, feedback visibility was not changed',
        variant: 'Error'
      });

      const key = isSubordinateFeedbackReceived
        ? MANAGERS_REQUEST_KEYS.subordinateFeedbackDetailsReceived({
            id: context?.feedbackId as string,
            subordinateId: context?.subordinateId as string
          })
        : REQUESTERS_REQUEST_KEYS.feedbackRequestedById({
            id: context?.feedbackId as string
          });

      queryClient.setQueryData(key, (old: FeedbackModel) => ({
        ...old,
        isVisibleToReceiver: context?.isVisibleToReceiver
      }));
    }
  });

  return { updateFeedbackVisibility, ...rest };
}
