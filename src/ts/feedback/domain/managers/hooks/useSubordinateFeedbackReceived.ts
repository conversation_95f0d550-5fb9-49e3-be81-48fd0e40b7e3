import { logger } from '@services/logger';

import { FeedbackService, useFeedbackServiceApi } from '@/feedback/api';
import { DEFAULT_FEEDBACK_REQUESTS_PROPS } from '@/feedback/constants';
import { transformToFeedback } from '@/feedback/domain/transformers';

export const useSubordinateFeedbackReceived = (subordinateId: string) => {
  const { managersApi } = useFeedbackServiceApi();
  return async (
    params: FeedbackService.SearchMySubordinateReceivedFeedbackRequest = DEFAULT_FEEDBACK_REQUESTS_PROPS
  ) => {
    try {
      const res =
        await managersApi.v2ManagersMeSubordinatesSubordinateIdReceivedFeedbackSearchPost(
          {
            subordinateId,
            searchMySubordinateReceivedFeedbackRequest: params
          }
        );
      return transformToFeedback(res);
    } catch (error) {
      logger.error(
        'Failed to load v1ManagersMeSubordinatesSubordinateIdReceivedFeedbackSearchPost',
        error
      );

      throw error;
    }
  };
};
