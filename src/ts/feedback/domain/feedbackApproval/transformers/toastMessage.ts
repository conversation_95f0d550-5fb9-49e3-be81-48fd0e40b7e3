import { ActionIds } from '@/components';

export const getSuccessToastMessage = (actionId: ActionIds): string => {
  switch (actionId) {
    case ActionIds.Finalize:
    case ActionIds.Submit:
      return 'Feedback was successfully submitted';
    case ActionIds.Decline:
      return 'Feedback was declined successfully';
    case ActionIds.Cancel:
    case ActionIds.Canceled:
      return 'Feedback request was canceled successfully';
    default:
      return 'Feedback action was successfully submitted';
  }
};

export const getErrorToastMessage = (actionId: ActionIds): string => {
  switch (actionId) {
    case ActionIds.Finalize:
    case ActionIds.Submit:
      return 'An error occurred, feedback was not submitted';
    case ActionIds.Decline:
      return 'An error occurred, feedback was not declined';
    case ActionIds.Cancel:
    case ActionIds.Canceled:
      return 'An error occurred, feedback request was not canceled';
    default:
      return 'An error occurred, feedback action was not submitted';
  }
};
