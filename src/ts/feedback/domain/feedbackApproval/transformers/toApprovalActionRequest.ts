import { FeedbackService } from '@/feedback/api';

import { FeedbackActionPayloadModel, DeclineFeedbackReason } from '../models';

const declineReasonMap: Record<
  DeclineFeedbackReason,
  FeedbackService.FeedbackDeclineReasons
> = {
  [DeclineFeedbackReason.LackOfRelevantInput]: 'lackOfRelevantInput',
  [DeclineFeedbackReason.UnclearExpectations]: 'unclearExpectations',
  [DeclineFeedbackReason.WaitingForObservableResults]:
    'waitingForObservableResults',
  [DeclineFeedbackReason.OtherReason]: 'otherReason'
};

export const toApprovalActionRequest = (
  rawData: FeedbackActionPayloadModel
): FeedbackService.ApprovalActionRequest => ({
  actionId: rawData.actionId,
  comment: rawData.comment,
  feedback: rawData.feedbackText,
  declineReason: rawData.declineReason
    ? declineReasonMap[rawData.declineReason]
    : undefined,
  rating: rawData.rating
});
