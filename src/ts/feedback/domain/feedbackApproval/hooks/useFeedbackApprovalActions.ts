import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { STALE_TIME } from '@/constants';
import { useFeedbackServiceApi } from '@/feedback/api';
import { logger } from '@/logger';

import { FEEDBACK_APPROVAL_REQUEST_KEYS } from './feedbackApprovalRequestKeys';

import { toApprovalActions } from '../transformers';

type Input = {
  requestId: string;
};

export const useGetFeedbackApprovalActions = (params: Input) => {
  const { approvalsApi } = useFeedbackServiceApi();
  return useCallback(async () => {
    try {
      return approvalsApi.v1ApprovalsRequestIdActionsGet(params);
    } catch (error) {
      logger.error('Failed to load v1ApprovalsRequestIdActionsGet', error);

      throw error;
    }
  }, [approvalsApi, params]);
};

export const useFeedbackApprovalActions = (params: Input) => {
  const queryFn = useGetFeedbackApprovalActions(params);

  return useQuery({
    queryFn,
    staleTime: STALE_TIME.MINUTES.TEN,
    queryKey: FEEDBACK_APPROVAL_REQUEST_KEYS.getApprovalActions(params),
    select: toApprovalActions
  });
};
