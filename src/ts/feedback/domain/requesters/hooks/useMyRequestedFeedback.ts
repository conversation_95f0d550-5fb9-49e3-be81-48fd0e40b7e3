import { logger } from '@services/logger';

import { FeedbackService, useFeedbackServiceApi } from '@/feedback/api';
import { transformToFeedback } from '@/feedback/domain/transformers';

export const useMyRequestedFeedback = () => {
  const { requestersApi } = useFeedbackServiceApi();
  return async (input: FeedbackService.SearchMyRequestedFeedbackRequest) => {
    try {
      const res = await requestersApi.v2RequestersMeFeedbackSearchPost({
        searchMyRequestedFeedbackRequest: input
      });
      return transformToFeedback(res);
    } catch (error) {
      logger.error('Failed to load v1RequestersMeFeedbackSearchPost', error);

      throw error;
    }
  };
};
