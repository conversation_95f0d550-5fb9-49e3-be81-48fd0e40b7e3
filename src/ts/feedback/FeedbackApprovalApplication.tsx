import { type FC, Suspense } from 'react';
import { useRoutes } from 'react-router-dom';

import { ErrorBoundaryPage } from '@oh/components';

import { FullScreenLoaderWrapped } from '@/components';
import { useApprovalInboxParams } from '@/core/hooks';
import { UuiContextProvider } from '@/providers';
import { OneHubFederatedApplicationProps } from '@/types';

import { useFeedbackApprovalRoutes } from './domain/feedbackApproval';
import { FeedbackFeatureFlagsProvider } from './providers/FeedbackFeatureFlagsProvider';

export const FeedbackApprovalApplication: FC<
  OneHubFederatedApplicationProps
> = ({ options }) => {
  const { entityType, requestId } = useApprovalInboxParams(options);

  const objectivesApprovalRoutes = useFeedbackApprovalRoutes({
    entityType,
    requestId
  });
  const element = useRoutes(objectivesApprovalRoutes);

  return (
    <UuiContextProvider>
      <Suspense fallback={<FullScreenLoaderWrapped />}>
        <ErrorBoundaryPage>
          <FeedbackFeatureFlagsProvider>
            {element}
            <div id="tooltip-area" />
          </FeedbackFeatureFlagsProvider>
        </ErrorBoundaryPage>
      </Suspense>
    </UuiContextProvider>
  );
};
