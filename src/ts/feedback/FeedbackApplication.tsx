import { type FC, Suspense } from 'react';
import { useRoutes } from 'react-router-dom';

import { AvatarLoaderProvider } from '@ot/onetalent-ui-kit';

import { ErrorBoundaryPage } from '@oh/components';

import { FullScreenLoaderWrapped, useAvatarLoadImage } from '@/components';
import { FeedbackVisibilityManager } from '@/feedback/pages';
import { UuiContextProvider } from '@/providers';

import { FeedbackFeatureFlagsProvider } from './providers/FeedbackFeatureFlagsProvider';
import { feedbackRoutes } from './routes';

export const FeedbackApplication: FC = () => {
  const element = useRoutes(feedbackRoutes);

  const loadImage = useAvatarLoadImage();

  return (
    <UuiContextProvider>
      <Suspense fallback={<FullScreenLoaderWrapped />}>
        <ErrorBoundaryPage>
          <FeedbackFeatureFlagsProvider>
            <AvatarLoaderProvider loadImage={loadImage}>
              {element}
              <FeedbackVisibilityManager />
              <div id="tooltip-area" />
            </AvatarLoaderProvider>
          </FeedbackFeatureFlagsProvider>
        </ErrorBoundaryPage>
      </Suspense>
    </UuiContextProvider>
  );
};
