import { type FC, type PropsWithChildren, useEffect, useMemo } from 'react';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import type { FederationApp } from '@oh/contracts';

import { useAppConfig, AppContext } from '@/contexts';
import { logger } from '@/logger';
import 'scss/index.scss';

dayjs.extend(utc);

export type AppProviderProps = PropsWithChildren<FederationApp>;

export const AppProvider: FC<AppProviderProps> = ({
  children,
  options,
  messageToHostFunction
}) => {
  const { appConfig } = useAppConfig();
  const { acquireToken } = options;

  useEffect(() => {
    logger.information('AppProvider Initialized');
  }, []);

  const appId = appConfig.ONETALENT_APP_ID;

  const appContextValue = useMemo(
    () => ({
      appId,
      acquireToken,
      options,
      messageToHostFunction
    }),
    [acquireToken, appId, messageToHostFunction, options]
  );

  return (
    <AppContext.Provider value={appContextValue}>
      {children}
    </AppContext.Provider>
  );
};
