/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface UserGroupPermissionCreateRequest
 */
export interface UserGroupPermissionCreateRequest {
    /**
     * 
     * @type {string}
     * @memberof UserGroupPermissionCreateRequest
     */
    userGroupId: string;
    /**
     * 
     * @type {string}
     * @memberof UserGroupPermissionCreateRequest
     */
    role: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof UserGroupPermissionCreateRequest
     */
    skillGroupIds: Array<string>;
}

/**
 * Check if a given object implements the UserGroupPermissionCreateRequest interface.
 */
export function instanceOfUserGroupPermissionCreateRequest(value: object): value is UserGroupPermissionCreateRequest {
    if (!('userGroupId' in value) || value['userGroupId'] === undefined) return false;
    if (!('role' in value) || value['role'] === undefined) return false;
    if (!('skillGroupIds' in value) || value['skillGroupIds'] === undefined) return false;
    return true;
}

export function UserGroupPermissionCreateRequestFromJSON(json: any): UserGroupPermissionCreateRequest {
    return UserGroupPermissionCreateRequestFromJSONTyped(json, false);
}

export function UserGroupPermissionCreateRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserGroupPermissionCreateRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'userGroupId': json['userGroupId'],
        'role': json['role'],
        'skillGroupIds': json['skillGroupIds'],
    };
}

export function UserGroupPermissionCreateRequestToJSON(json: any): UserGroupPermissionCreateRequest {
    return UserGroupPermissionCreateRequestToJSONTyped(json, false);
}

export function UserGroupPermissionCreateRequestToJSONTyped(value?: UserGroupPermissionCreateRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'userGroupId': value['userGroupId'],
        'role': value['role'],
        'skillGroupIds': value['skillGroupIds'],
    };
}

