/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SkillsManagementSectionViewModel } from './SkillsManagementSectionViewModel';
import {
    SkillsManagementSectionViewModelFromJSON,
    SkillsManagementSectionViewModelFromJSONTyped,
    SkillsManagementSectionViewModelToJSON,
    SkillsManagementSectionViewModelToJSONTyped,
} from './SkillsManagementSectionViewModel';
import type { ApprovalsSectionViewModel } from './ApprovalsSectionViewModel';
import {
    ApprovalsSectionViewModelFromJSON,
    ApprovalsSectionViewModelFromJSONTyped,
    ApprovalsSectionViewModelToJSON,
    ApprovalsSectionViewModelToJSONTyped,
} from './ApprovalsSectionViewModel';
import type { WorkConditionSectionViewModel } from './WorkConditionSectionViewModel';
import {
    WorkConditionSectionViewModelFromJSON,
    WorkConditionSectionViewModelFromJSONTyped,
    WorkConditionSectionViewModelToJSON,
    WorkConditionSectionViewModelToJSONTyped,
} from './WorkConditionSectionViewModel';
import type { PurposeSectionViewModel } from './PurposeSectionViewModel';
import {
    PurposeSectionViewModelFromJSON,
    PurposeSectionViewModelFromJSONTyped,
    PurposeSectionViewModelToJSON,
    PurposeSectionViewModelToJSONTyped,
} from './PurposeSectionViewModel';
import type { DimensionsSectionViewModel } from './DimensionsSectionViewModel';
import {
    DimensionsSectionViewModelFromJSON,
    DimensionsSectionViewModelFromJSONTyped,
    DimensionsSectionViewModelToJSON,
    DimensionsSectionViewModelToJSONTyped,
} from './DimensionsSectionViewModel';
import type { WorkQualificationSectionViewModel } from './WorkQualificationSectionViewModel';
import {
    WorkQualificationSectionViewModelFromJSON,
    WorkQualificationSectionViewModelFromJSONTyped,
    WorkQualificationSectionViewModelToJSON,
    WorkQualificationSectionViewModelToJSONTyped,
} from './WorkQualificationSectionViewModel';
import type { ConfidentialityLevelType } from './ConfidentialityLevelType';
import {
    ConfidentialityLevelTypeFromJSON,
    ConfidentialityLevelTypeFromJSONTyped,
    ConfidentialityLevelTypeToJSON,
    ConfidentialityLevelTypeToJSONTyped,
} from './ConfidentialityLevelType';
import type { CommunicationAndWorkingRelationshipSectionViewModel } from './CommunicationAndWorkingRelationshipSectionViewModel';
import {
    CommunicationAndWorkingRelationshipSectionViewModelFromJSON,
    CommunicationAndWorkingRelationshipSectionViewModelFromJSONTyped,
    CommunicationAndWorkingRelationshipSectionViewModelToJSON,
    CommunicationAndWorkingRelationshipSectionViewModelToJSONTyped,
} from './CommunicationAndWorkingRelationshipSectionViewModel';
import type { KeyAccountabilitiesSectionViewModel } from './KeyAccountabilitiesSectionViewModel';
import {
    KeyAccountabilitiesSectionViewModelFromJSON,
    KeyAccountabilitiesSectionViewModelFromJSONTyped,
    KeyAccountabilitiesSectionViewModelToJSON,
    KeyAccountabilitiesSectionViewModelToJSONTyped,
} from './KeyAccountabilitiesSectionViewModel';
import type { KpiSectionViewModel } from './KpiSectionViewModel';
import {
    KpiSectionViewModelFromJSON,
    KpiSectionViewModelFromJSONTyped,
    KpiSectionViewModelToJSON,
    KpiSectionViewModelToJSONTyped,
} from './KpiSectionViewModel';
import type { JobDescriptionStatusViewModel } from './JobDescriptionStatusViewModel';
import {
    JobDescriptionStatusViewModelFromJSON,
    JobDescriptionStatusViewModelFromJSONTyped,
    JobDescriptionStatusViewModelToJSON,
    JobDescriptionStatusViewModelToJSONTyped,
} from './JobDescriptionStatusViewModel';

/**
 * 
 * @export
 * @interface JobDescriptionViewModel
 */
export interface JobDescriptionViewModel {
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionViewModel
     */
    id: string;
    /**
     * 
     * @type {number}
     * @memberof JobDescriptionViewModel
     */
    version: number;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionViewModel
     */
    title: string;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionViewModel
     */
    validFrom?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionViewModel
     */
    validTo?: Date | null;
    /**
     * 
     * @type {JobDescriptionStatusViewModel}
     * @memberof JobDescriptionViewModel
     */
    status?: JobDescriptionStatusViewModel;
    /**
     * 
     * @type {ConfidentialityLevelType}
     * @memberof JobDescriptionViewModel
     */
    confidentialityLevel: ConfidentialityLevelType;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionViewModel
     */
    lastApprovedBy?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionViewModel
     */
    approvedDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionViewModel
     */
    modifiedBy?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionViewModel
     */
    modifiedDate?: Date | null;
    /**
     * 
     * @type {DimensionsSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    dimensions?: DimensionsSectionViewModel;
    /**
     * 
     * @type {SkillsManagementSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    skillsManagement?: SkillsManagementSectionViewModel;
    /**
     * 
     * @type {ApprovalsSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    approvals?: ApprovalsSectionViewModel;
    /**
     * 
     * @type {PurposeSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    purpose?: PurposeSectionViewModel;
    /**
     * 
     * @type {KeyAccountabilitiesSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    keyAccountabilities?: KeyAccountabilitiesSectionViewModel;
    /**
     * 
     * @type {CommunicationAndWorkingRelationshipSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    communicationAndWorkingRelationship?: CommunicationAndWorkingRelationshipSectionViewModel;
    /**
     * 
     * @type {WorkQualificationSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    workQualification?: WorkQualificationSectionViewModel;
    /**
     * 
     * @type {WorkConditionSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    workCondition?: WorkConditionSectionViewModel;
    /**
     * 
     * @type {KpiSectionViewModel}
     * @memberof JobDescriptionViewModel
     */
    kpi?: KpiSectionViewModel;
}



/**
 * Check if a given object implements the JobDescriptionViewModel interface.
 */
export function instanceOfJobDescriptionViewModel(value: object): value is JobDescriptionViewModel {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('version' in value) || value['version'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('confidentialityLevel' in value) || value['confidentialityLevel'] === undefined) return false;
    return true;
}

export function JobDescriptionViewModelFromJSON(json: any): JobDescriptionViewModel {
    return JobDescriptionViewModelFromJSONTyped(json, false);
}

export function JobDescriptionViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): JobDescriptionViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'version': json['version'],
        'title': json['title'],
        'validFrom': json['validFrom'] == null ? undefined : (new Date(json['validFrom'])),
        'validTo': json['validTo'] == null ? undefined : (new Date(json['validTo'])),
        'status': json['status'] == null ? undefined : JobDescriptionStatusViewModelFromJSON(json['status']),
        'confidentialityLevel': ConfidentialityLevelTypeFromJSON(json['confidentialityLevel']),
        'lastApprovedBy': json['lastApprovedBy'] == null ? undefined : json['lastApprovedBy'],
        'approvedDate': json['approvedDate'] == null ? undefined : (new Date(json['approvedDate'])),
        'modifiedBy': json['modifiedBy'] == null ? undefined : json['modifiedBy'],
        'modifiedDate': json['modifiedDate'] == null ? undefined : (new Date(json['modifiedDate'])),
        'dimensions': json['dimensions'] == null ? undefined : DimensionsSectionViewModelFromJSON(json['dimensions']),
        'skillsManagement': json['skillsManagement'] == null ? undefined : SkillsManagementSectionViewModelFromJSON(json['skillsManagement']),
        'approvals': json['approvals'] == null ? undefined : ApprovalsSectionViewModelFromJSON(json['approvals']),
        'purpose': json['purpose'] == null ? undefined : PurposeSectionViewModelFromJSON(json['purpose']),
        'keyAccountabilities': json['keyAccountabilities'] == null ? undefined : KeyAccountabilitiesSectionViewModelFromJSON(json['keyAccountabilities']),
        'communicationAndWorkingRelationship': json['communicationAndWorkingRelationship'] == null ? undefined : CommunicationAndWorkingRelationshipSectionViewModelFromJSON(json['communicationAndWorkingRelationship']),
        'workQualification': json['workQualification'] == null ? undefined : WorkQualificationSectionViewModelFromJSON(json['workQualification']),
        'workCondition': json['workCondition'] == null ? undefined : WorkConditionSectionViewModelFromJSON(json['workCondition']),
        'kpi': json['kpi'] == null ? undefined : KpiSectionViewModelFromJSON(json['kpi']),
    };
}

export function JobDescriptionViewModelToJSON(json: any): JobDescriptionViewModel {
    return JobDescriptionViewModelToJSONTyped(json, false);
}

export function JobDescriptionViewModelToJSONTyped(value?: JobDescriptionViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'version': value['version'],
        'title': value['title'],
        'validFrom': value['validFrom'] == null ? undefined : ((value['validFrom'] as any).toISOString()),
        'validTo': value['validTo'] == null ? undefined : ((value['validTo'] as any).toISOString()),
        'status': JobDescriptionStatusViewModelToJSON(value['status']),
        'confidentialityLevel': ConfidentialityLevelTypeToJSON(value['confidentialityLevel']),
        'lastApprovedBy': value['lastApprovedBy'],
        'approvedDate': value['approvedDate'] == null ? undefined : ((value['approvedDate'] as any).toISOString()),
        'modifiedBy': value['modifiedBy'],
        'modifiedDate': value['modifiedDate'] == null ? undefined : ((value['modifiedDate'] as any).toISOString()),
        'dimensions': DimensionsSectionViewModelToJSON(value['dimensions']),
        'skillsManagement': SkillsManagementSectionViewModelToJSON(value['skillsManagement']),
        'approvals': ApprovalsSectionViewModelToJSON(value['approvals']),
        'purpose': PurposeSectionViewModelToJSON(value['purpose']),
        'keyAccountabilities': KeyAccountabilitiesSectionViewModelToJSON(value['keyAccountabilities']),
        'communicationAndWorkingRelationship': CommunicationAndWorkingRelationshipSectionViewModelToJSON(value['communicationAndWorkingRelationship']),
        'workQualification': WorkQualificationSectionViewModelToJSON(value['workQualification']),
        'workCondition': WorkConditionSectionViewModelToJSON(value['workCondition']),
        'kpi': KpiSectionViewModelToJSON(value['kpi']),
    };
}

