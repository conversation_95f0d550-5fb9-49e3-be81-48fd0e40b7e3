/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SkillFiltersRequest } from './SkillFiltersRequest';
import {
    SkillFiltersRequestFromJSON,
    SkillFiltersRequestFromJSONTyped,
    SkillFiltersRequestToJSON,
    SkillFiltersRequestToJSONTyped,
} from './SkillFiltersRequest';

/**
 * 
 * @export
 * @interface GetPositionTitlesRequest
 */
export interface GetPositionTitlesRequest {
    /**
     * 
     * @type {string}
     * @memberof GetPositionTitlesRequest
     */
    search?: string | null;
    /**
     * 
     * @type {string}
     * @memberof GetPositionTitlesRequest
     */
    orderBy?: string | null;
    /**
     * 
     * @type {number}
     * @memberof GetPositionTitlesRequest
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof GetPositionTitlesRequest
     */
    pageSize?: number | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    companies?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    positionLevels?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    organizationUnitTypes?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    directorates?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    deputyDirectorates?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    functions?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    departments?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    divisions?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    locations?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetPositionTitlesRequest
     */
    employmentTypes?: Array<string> | null;
    /**
     * 
     * @type {SkillFiltersRequest}
     * @memberof GetPositionTitlesRequest
     */
    skillFilters?: SkillFiltersRequest;
}

/**
 * Check if a given object implements the GetPositionTitlesRequest interface.
 */
export function instanceOfGetPositionTitlesRequest(value: object): value is GetPositionTitlesRequest {
    return true;
}

export function GetPositionTitlesRequestFromJSON(json: any): GetPositionTitlesRequest {
    return GetPositionTitlesRequestFromJSONTyped(json, false);
}

export function GetPositionTitlesRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetPositionTitlesRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'search': json['search'] == null ? undefined : json['search'],
        'orderBy': json['orderBy'] == null ? undefined : json['orderBy'],
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
        'companies': json['companies'] == null ? undefined : json['companies'],
        'positionLevels': json['positionLevels'] == null ? undefined : json['positionLevels'],
        'organizationUnitTypes': json['organizationUnitTypes'] == null ? undefined : json['organizationUnitTypes'],
        'directorates': json['directorates'] == null ? undefined : json['directorates'],
        'deputyDirectorates': json['deputyDirectorates'] == null ? undefined : json['deputyDirectorates'],
        'functions': json['functions'] == null ? undefined : json['functions'],
        'departments': json['departments'] == null ? undefined : json['departments'],
        'divisions': json['divisions'] == null ? undefined : json['divisions'],
        'locations': json['locations'] == null ? undefined : json['locations'],
        'employmentTypes': json['employmentTypes'] == null ? undefined : json['employmentTypes'],
        'skillFilters': json['skillFilters'] == null ? undefined : SkillFiltersRequestFromJSON(json['skillFilters']),
    };
}

export function GetPositionTitlesRequestToJSON(json: any): GetPositionTitlesRequest {
    return GetPositionTitlesRequestToJSONTyped(json, false);
}

export function GetPositionTitlesRequestToJSONTyped(value?: GetPositionTitlesRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'search': value['search'],
        'orderBy': value['orderBy'],
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
        'companies': value['companies'],
        'positionLevels': value['positionLevels'],
        'organizationUnitTypes': value['organizationUnitTypes'],
        'directorates': value['directorates'],
        'deputyDirectorates': value['deputyDirectorates'],
        'functions': value['functions'],
        'departments': value['departments'],
        'divisions': value['divisions'],
        'locations': value['locations'],
        'employmentTypes': value['employmentTypes'],
        'skillFilters': SkillFiltersRequestToJSON(value['skillFilters']),
    };
}

