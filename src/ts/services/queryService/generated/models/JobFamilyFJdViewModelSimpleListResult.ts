/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { JobFamilyFJdViewModel } from './JobFamilyFJdViewModel';
import {
    JobFamilyFJdViewModelFromJSON,
    JobFamilyFJdViewModelFromJSONTyped,
    JobFamilyFJdViewModelToJSON,
    JobFamilyFJdViewModelToJSONTyped,
} from './JobFamilyFJdViewModel';

/**
 * 
 * @export
 * @interface JobFamilyFJdViewModelSimpleListResult
 */
export interface JobFamilyFJdViewModelSimpleListResult {
    /**
     * 
     * @type {Array<JobFamilyFJdViewModel>}
     * @memberof JobFamilyFJdViewModelSimpleListResult
     */
    items: Array<JobFamilyFJdViewModel>;
}

/**
 * Check if a given object implements the JobFamilyFJdViewModelSimpleListResult interface.
 */
export function instanceOfJobFamilyFJdViewModelSimpleListResult(value: object): value is JobFamilyFJdViewModelSimpleListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    return true;
}

export function JobFamilyFJdViewModelSimpleListResultFromJSON(json: any): JobFamilyFJdViewModelSimpleListResult {
    return JobFamilyFJdViewModelSimpleListResultFromJSONTyped(json, false);
}

export function JobFamilyFJdViewModelSimpleListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): JobFamilyFJdViewModelSimpleListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(JobFamilyFJdViewModelFromJSON)),
    };
}

export function JobFamilyFJdViewModelSimpleListResultToJSON(json: any): JobFamilyFJdViewModelSimpleListResult {
    return JobFamilyFJdViewModelSimpleListResultToJSONTyped(json, false);
}

export function JobFamilyFJdViewModelSimpleListResultToJSONTyped(value?: JobFamilyFJdViewModelSimpleListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(JobFamilyFJdViewModelToJSON)),
    };
}

