/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface UpdateJobDescriptionProfileResponse
 */
export interface UpdateJobDescriptionProfileResponse {
    /**
     * 
     * @type {string}
     * @memberof UpdateJobDescriptionProfileResponse
     */
    jobDescriptionId: string;
}

/**
 * Check if a given object implements the UpdateJobDescriptionProfileResponse interface.
 */
export function instanceOfUpdateJobDescriptionProfileResponse(value: object): value is UpdateJobDescriptionProfileResponse {
    if (!('jobDescriptionId' in value) || value['jobDescriptionId'] === undefined) return false;
    return true;
}

export function UpdateJobDescriptionProfileResponseFromJSON(json: any): UpdateJobDescriptionProfileResponse {
    return UpdateJobDescriptionProfileResponseFromJSONTyped(json, false);
}

export function UpdateJobDescriptionProfileResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): UpdateJobDescriptionProfileResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'jobDescriptionId': json['jobDescriptionId'],
    };
}

export function UpdateJobDescriptionProfileResponseToJSON(json: any): UpdateJobDescriptionProfileResponse {
    return UpdateJobDescriptionProfileResponseToJSONTyped(json, false);
}

export function UpdateJobDescriptionProfileResponseToJSONTyped(value?: UpdateJobDescriptionProfileResponse | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'jobDescriptionId': value['jobDescriptionId'],
    };
}

