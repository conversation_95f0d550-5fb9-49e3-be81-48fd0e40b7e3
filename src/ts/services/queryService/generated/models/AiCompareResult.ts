/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface AiCompareResult
 */
export interface AiCompareResult {
    /**
     * 
     * @type {string}
     * @memberof AiCompareResult
     */
    comparison: string;
    /**
     * 
     * @type {number}
     * @memberof AiCompareResult
     */
    similarityScore: number;
}

/**
 * Check if a given object implements the AiCompareResult interface.
 */
export function instanceOfAiCompareResult(value: object): value is AiCompareResult {
    if (!('comparison' in value) || value['comparison'] === undefined) return false;
    if (!('similarityScore' in value) || value['similarityScore'] === undefined) return false;
    return true;
}

export function AiCompareResultFromJSON(json: any): AiCompareResult {
    return AiCompareResultFromJSONTyped(json, false);
}

export function AiCompareResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): AiCompareResult {
    if (json == null) {
        return json;
    }
    return {
        
        'comparison': json['comparison'],
        'similarityScore': json['similarityScore'],
    };
}

export function AiCompareResultToJSON(json: any): AiCompareResult {
    return AiCompareResultToJSONTyped(json, false);
}

export function AiCompareResultToJSONTyped(value?: AiCompareResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'comparison': value['comparison'],
        'similarityScore': value['similarityScore'],
    };
}

