/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SubFamilyJdViewModel
 */
export interface SubFamilyJdViewModel {
    /**
     * 
     * @type {string}
     * @memberof SubFamilyJdViewModel
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SubFamilyJdViewModel
     */
    name: string;
}

/**
 * Check if a given object implements the SubFamilyJdViewModel interface.
 */
export function instanceOfSubFamilyJdViewModel(value: object): value is SubFamilyJdViewModel {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function SubFamilyJdViewModelFromJSON(json: any): SubFamilyJdViewModel {
    return SubFamilyJdViewModelFromJSONTyped(json, false);
}

export function SubFamilyJdViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): SubFamilyJdViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function SubFamilyJdViewModelToJSON(json: any): SubFamilyJdViewModel {
    return SubFamilyJdViewModelToJSONTyped(json, false);
}

export function SubFamilyJdViewModelToJSONTyped(value?: SubFamilyJdViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

