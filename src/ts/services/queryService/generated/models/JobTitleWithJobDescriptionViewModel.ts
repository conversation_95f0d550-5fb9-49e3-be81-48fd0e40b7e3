/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { JobDescriptionListViewModel } from './JobDescriptionListViewModel';
import {
    JobDescriptionListViewModelFromJSON,
    JobDescriptionListViewModelFromJSONTyped,
    JobDescriptionListViewModelToJSON,
    JobDescriptionListViewModelToJSONTyped,
} from './JobDescriptionListViewModel';
import type { AvailableActionsViewModel } from './AvailableActionsViewModel';
import {
    AvailableActionsViewModelFromJSON,
    AvailableActionsViewModelFromJSONTyped,
    AvailableActionsViewModelToJSON,
    AvailableActionsViewModelToJSONTyped,
} from './AvailableActionsViewModel';

/**
 * 
 * @export
 * @interface JobTitleWithJobDescriptionViewModel
 */
export interface JobTitleWithJobDescriptionViewModel {
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    id?: string | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    name?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    jobFamilies?: Array<string> | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    pathFromRoot?: string | null;
    /**
     * 
     * @type {JobDescriptionListViewModel}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    jobDescription?: JobDescriptionListViewModel;
    /**
     * 
     * @type {AvailableActionsViewModel}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    availableActions?: AvailableActionsViewModel;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    externalId?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    createdDateTime?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    lastModifiedDateTime?: string | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    technicalNontechnical?: string | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    gradeRangeFrom?: string | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    gradeRangeTo?: string | null;
    /**
     * 
     * @type {string}
     * @memberof JobTitleWithJobDescriptionViewModel
     */
    jobEvaluationStatus?: string | null;
}

/**
 * Check if a given object implements the JobTitleWithJobDescriptionViewModel interface.
 */
export function instanceOfJobTitleWithJobDescriptionViewModel(value: object): value is JobTitleWithJobDescriptionViewModel {
    return true;
}

export function JobTitleWithJobDescriptionViewModelFromJSON(json: any): JobTitleWithJobDescriptionViewModel {
    return JobTitleWithJobDescriptionViewModelFromJSONTyped(json, false);
}

export function JobTitleWithJobDescriptionViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): JobTitleWithJobDescriptionViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'jobFamilies': json['jobFamilies'] == null ? undefined : json['jobFamilies'],
        'pathFromRoot': json['pathFromRoot'] == null ? undefined : json['pathFromRoot'],
        'jobDescription': json['jobDescription'] == null ? undefined : JobDescriptionListViewModelFromJSON(json['jobDescription']),
        'availableActions': json['availableActions'] == null ? undefined : AvailableActionsViewModelFromJSON(json['availableActions']),
        'externalId': json['externalId'] == null ? undefined : json['externalId'],
        'createdDateTime': json['createdDateTime'] == null ? undefined : (new Date(json['createdDateTime'])),
        'lastModifiedDateTime': json['lastModifiedDateTime'] == null ? undefined : json['lastModifiedDateTime'],
        'technicalNontechnical': json['technicalNontechnical'] == null ? undefined : json['technicalNontechnical'],
        'gradeRangeFrom': json['gradeRangeFrom'] == null ? undefined : json['gradeRangeFrom'],
        'gradeRangeTo': json['gradeRangeTo'] == null ? undefined : json['gradeRangeTo'],
        'jobEvaluationStatus': json['jobEvaluationStatus'] == null ? undefined : json['jobEvaluationStatus'],
    };
}

export function JobTitleWithJobDescriptionViewModelToJSON(json: any): JobTitleWithJobDescriptionViewModel {
    return JobTitleWithJobDescriptionViewModelToJSONTyped(json, false);
}

export function JobTitleWithJobDescriptionViewModelToJSONTyped(value?: JobTitleWithJobDescriptionViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'jobFamilies': value['jobFamilies'],
        'pathFromRoot': value['pathFromRoot'],
        'jobDescription': JobDescriptionListViewModelToJSON(value['jobDescription']),
        'availableActions': AvailableActionsViewModelToJSON(value['availableActions']),
        'externalId': value['externalId'],
        'createdDateTime': value['createdDateTime'] == null ? undefined : ((value['createdDateTime'] as any).toISOString()),
        'lastModifiedDateTime': value['lastModifiedDateTime'],
        'technicalNontechnical': value['technicalNontechnical'],
        'gradeRangeFrom': value['gradeRangeFrom'],
        'gradeRangeTo': value['gradeRangeTo'],
        'jobEvaluationStatus': value['jobEvaluationStatus'],
    };
}

