/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SkillSourceViewModel
 */
export interface SkillSourceViewModel {
    /**
     * 
     * @type {string}
     * @memberof SkillSourceViewModel
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SkillSourceViewModel
     */
    name: string;
}

/**
 * Check if a given object implements the SkillSourceViewModel interface.
 */
export function instanceOfSkillSourceViewModel(value: object): value is SkillSourceViewModel {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function SkillSourceViewModelFromJSON(json: any): SkillSourceViewModel {
    return SkillSourceViewModelFromJSONTyped(json, false);
}

export function SkillSourceViewModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): SkillSourceViewModel {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function SkillSourceViewModelToJSON(json: any): SkillSourceViewModel {
    return SkillSourceViewModelToJSONTyped(json, false);
}

export function SkillSourceViewModelToJSONTyped(value?: SkillSourceViewModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

