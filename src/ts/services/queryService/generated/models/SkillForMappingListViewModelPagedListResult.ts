/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';
import type { SkillForMappingListViewModel } from './SkillForMappingListViewModel';
import {
    SkillForMappingListViewModelFromJSON,
    SkillForMappingListViewModelFromJSONTyped,
    SkillForMappingListViewModelToJSON,
    SkillForMappingListViewModelToJSONTyped,
} from './SkillForMappingListViewModel';

/**
 * 
 * @export
 * @interface SkillForMappingListViewModelPagedListResult
 */
export interface SkillForMappingListViewModelPagedListResult {
    /**
     * 
     * @type {Array<SkillForMappingListViewModel>}
     * @memberof SkillForMappingListViewModelPagedListResult
     */
    items: Array<SkillForMappingListViewModel>;
    /**
     * 
     * @type {PaginationData}
     * @memberof SkillForMappingListViewModelPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the SkillForMappingListViewModelPagedListResult interface.
 */
export function instanceOfSkillForMappingListViewModelPagedListResult(value: object): value is SkillForMappingListViewModelPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function SkillForMappingListViewModelPagedListResultFromJSON(json: any): SkillForMappingListViewModelPagedListResult {
    return SkillForMappingListViewModelPagedListResultFromJSONTyped(json, false);
}

export function SkillForMappingListViewModelPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): SkillForMappingListViewModelPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(SkillForMappingListViewModelFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

export function SkillForMappingListViewModelPagedListResultToJSON(json: any): SkillForMappingListViewModelPagedListResult {
    return SkillForMappingListViewModelPagedListResultToJSONTyped(json, false);
}

export function SkillForMappingListViewModelPagedListResultToJSONTyped(value?: SkillForMappingListViewModelPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(SkillForMappingListViewModelToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

