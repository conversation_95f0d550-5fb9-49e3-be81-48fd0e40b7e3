/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SkillMappingSuggestionChanges } from './SkillMappingSuggestionChanges';
import {
    SkillMappingSuggestionChangesFromJSON,
    SkillMappingSuggestionChangesFromJSONTyped,
    SkillMappingSuggestionChangesToJSON,
    SkillMappingSuggestionChangesToJSONTyped,
} from './SkillMappingSuggestionChanges';

/**
 * 
 * @export
 * @interface SkillMappingSuggestionsRequest
 */
export interface SkillMappingSuggestionsRequest {
    /**
     * 
     * @type {string}
     * @memberof SkillMappingSuggestionsRequest
     */
    objectId: string;
    /**
     * 
     * @type {Array<SkillMappingSuggestionChanges>}
     * @memberof SkillMappingSuggestionsRequest
     */
    changes: Array<SkillMappingSuggestionChanges> | null;
}

/**
 * Check if a given object implements the SkillMappingSuggestionsRequest interface.
 */
export function instanceOfSkillMappingSuggestionsRequest(value: object): value is SkillMappingSuggestionsRequest {
    if (!('objectId' in value) || value['objectId'] === undefined) return false;
    if (!('changes' in value) || value['changes'] === undefined) return false;
    return true;
}

export function SkillMappingSuggestionsRequestFromJSON(json: any): SkillMappingSuggestionsRequest {
    return SkillMappingSuggestionsRequestFromJSONTyped(json, false);
}

export function SkillMappingSuggestionsRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SkillMappingSuggestionsRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'objectId': json['objectId'],
        'changes': (json['changes'] == null ? null : (json['changes'] as Array<any>).map(SkillMappingSuggestionChangesFromJSON)),
    };
}

export function SkillMappingSuggestionsRequestToJSON(json: any): SkillMappingSuggestionsRequest {
    return SkillMappingSuggestionsRequestToJSONTyped(json, false);
}

export function SkillMappingSuggestionsRequestToJSONTyped(value?: SkillMappingSuggestionsRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'objectId': value['objectId'],
        'changes': (value['changes'] == null ? null : (value['changes'] as Array<any>).map(SkillMappingSuggestionChangesToJSON)),
    };
}

