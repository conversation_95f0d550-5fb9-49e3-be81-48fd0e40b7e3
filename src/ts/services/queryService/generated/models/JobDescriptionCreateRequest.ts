/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.QueryService - PublicAPI
 * Public API documentation
 *
 * The version of the OpenAPI document: 0.0.1-beta
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { OdObjectRequest } from './OdObjectRequest';
import {
    OdObjectRequestFromJSON,
    OdObjectRequestFromJSONTyped,
    OdObjectRequestToJSON,
    OdObjectRequestToJSONTyped,
} from './OdObjectRequest';
import type { ApprovalsSectionRequest } from './ApprovalsSectionRequest';
import {
    ApprovalsSectionRequestFromJSON,
    ApprovalsSectionRequestFromJSONTyped,
    ApprovalsSectionRequestToJSON,
    ApprovalsSectionRequestToJSONTyped,
} from './ApprovalsSectionRequest';
import type { WorkQualificationSectionRequest } from './WorkQualificationSectionRequest';
import {
    WorkQualificationSectionRequestFromJSON,
    WorkQualificationSectionRequestFromJSONTyped,
    WorkQualificationSectionRequestToJSON,
    WorkQualificationSectionRequestToJSONTyped,
} from './WorkQualificationSectionRequest';
import type { KeyAccountabilitiesSectionRequest } from './KeyAccountabilitiesSectionRequest';
import {
    KeyAccountabilitiesSectionRequestFromJSON,
    KeyAccountabilitiesSectionRequestFromJSONTyped,
    KeyAccountabilitiesSectionRequestToJSON,
    KeyAccountabilitiesSectionRequestToJSONTyped,
} from './KeyAccountabilitiesSectionRequest';
import type { ConfidentialityLevelType } from './ConfidentialityLevelType';
import {
    ConfidentialityLevelTypeFromJSON,
    ConfidentialityLevelTypeFromJSONTyped,
    ConfidentialityLevelTypeToJSON,
    ConfidentialityLevelTypeToJSONTyped,
} from './ConfidentialityLevelType';
import type { CommunicationAndWorkingRelationshipSectionRequest } from './CommunicationAndWorkingRelationshipSectionRequest';
import {
    CommunicationAndWorkingRelationshipSectionRequestFromJSON,
    CommunicationAndWorkingRelationshipSectionRequestFromJSONTyped,
    CommunicationAndWorkingRelationshipSectionRequestToJSON,
    CommunicationAndWorkingRelationshipSectionRequestToJSONTyped,
} from './CommunicationAndWorkingRelationshipSectionRequest';
import type { DimensionsSectionRequest } from './DimensionsSectionRequest';
import {
    DimensionsSectionRequestFromJSON,
    DimensionsSectionRequestFromJSONTyped,
    DimensionsSectionRequestToJSON,
    DimensionsSectionRequestToJSONTyped,
} from './DimensionsSectionRequest';
import type { KpiSectionRequest } from './KpiSectionRequest';
import {
    KpiSectionRequestFromJSON,
    KpiSectionRequestFromJSONTyped,
    KpiSectionRequestToJSON,
    KpiSectionRequestToJSONTyped,
} from './KpiSectionRequest';
import type { SkillsManagementSectionRequest } from './SkillsManagementSectionRequest';
import {
    SkillsManagementSectionRequestFromJSON,
    SkillsManagementSectionRequestFromJSONTyped,
    SkillsManagementSectionRequestToJSON,
    SkillsManagementSectionRequestToJSONTyped,
} from './SkillsManagementSectionRequest';
import type { WorkConditionSectionRequest } from './WorkConditionSectionRequest';
import {
    WorkConditionSectionRequestFromJSON,
    WorkConditionSectionRequestFromJSONTyped,
    WorkConditionSectionRequestToJSON,
    WorkConditionSectionRequestToJSONTyped,
} from './WorkConditionSectionRequest';
import type { PurposeSectionRequest } from './PurposeSectionRequest';
import {
    PurposeSectionRequestFromJSON,
    PurposeSectionRequestFromJSONTyped,
    PurposeSectionRequestToJSON,
    PurposeSectionRequestToJSONTyped,
} from './PurposeSectionRequest';

/**
 * 
 * @export
 * @interface JobDescriptionCreateRequest
 */
export interface JobDescriptionCreateRequest {
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionCreateRequest
     */
    title: string;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionCreateRequest
     */
    jobDescriptionTemplateId?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionCreateRequest
     */
    validFrom?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof JobDescriptionCreateRequest
     */
    validTo?: Date | null;
    /**
     * 
     * @type {ConfidentialityLevelType}
     * @memberof JobDescriptionCreateRequest
     */
    confidentialityLevel?: ConfidentialityLevelType;
    /**
     * 
     * @type {string}
     * @memberof JobDescriptionCreateRequest
     */
    lastApprovedBy?: string | null;
    /**
     * 
     * @type {DimensionsSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    dimensions?: DimensionsSectionRequest;
    /**
     * 
     * @type {SkillsManagementSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    skillsManagement?: SkillsManagementSectionRequest;
    /**
     * 
     * @type {ApprovalsSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    approvals?: ApprovalsSectionRequest;
    /**
     * 
     * @type {PurposeSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    purpose?: PurposeSectionRequest;
    /**
     * 
     * @type {KeyAccountabilitiesSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    keyAccountabilities?: KeyAccountabilitiesSectionRequest;
    /**
     * 
     * @type {CommunicationAndWorkingRelationshipSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    communicationAndWorkingRelationship?: CommunicationAndWorkingRelationshipSectionRequest;
    /**
     * 
     * @type {WorkQualificationSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    workQualification?: WorkQualificationSectionRequest;
    /**
     * 
     * @type {WorkConditionSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    workCondition?: WorkConditionSectionRequest;
    /**
     * 
     * @type {KpiSectionRequest}
     * @memberof JobDescriptionCreateRequest
     */
    kpi?: KpiSectionRequest;
    /**
     * 
     * @type {OdObjectRequest}
     * @memberof JobDescriptionCreateRequest
     */
    odObject?: OdObjectRequest;
}



/**
 * Check if a given object implements the JobDescriptionCreateRequest interface.
 */
export function instanceOfJobDescriptionCreateRequest(value: object): value is JobDescriptionCreateRequest {
    if (!('title' in value) || value['title'] === undefined) return false;
    return true;
}

export function JobDescriptionCreateRequestFromJSON(json: any): JobDescriptionCreateRequest {
    return JobDescriptionCreateRequestFromJSONTyped(json, false);
}

export function JobDescriptionCreateRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): JobDescriptionCreateRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
        'jobDescriptionTemplateId': json['jobDescriptionTemplateId'] == null ? undefined : json['jobDescriptionTemplateId'],
        'validFrom': json['validFrom'] == null ? undefined : (new Date(json['validFrom'])),
        'validTo': json['validTo'] == null ? undefined : (new Date(json['validTo'])),
        'confidentialityLevel': json['confidentialityLevel'] == null ? undefined : ConfidentialityLevelTypeFromJSON(json['confidentialityLevel']),
        'lastApprovedBy': json['lastApprovedBy'] == null ? undefined : json['lastApprovedBy'],
        'dimensions': json['dimensions'] == null ? undefined : DimensionsSectionRequestFromJSON(json['dimensions']),
        'skillsManagement': json['skillsManagement'] == null ? undefined : SkillsManagementSectionRequestFromJSON(json['skillsManagement']),
        'approvals': json['approvals'] == null ? undefined : ApprovalsSectionRequestFromJSON(json['approvals']),
        'purpose': json['purpose'] == null ? undefined : PurposeSectionRequestFromJSON(json['purpose']),
        'keyAccountabilities': json['keyAccountabilities'] == null ? undefined : KeyAccountabilitiesSectionRequestFromJSON(json['keyAccountabilities']),
        'communicationAndWorkingRelationship': json['communicationAndWorkingRelationship'] == null ? undefined : CommunicationAndWorkingRelationshipSectionRequestFromJSON(json['communicationAndWorkingRelationship']),
        'workQualification': json['workQualification'] == null ? undefined : WorkQualificationSectionRequestFromJSON(json['workQualification']),
        'workCondition': json['workCondition'] == null ? undefined : WorkConditionSectionRequestFromJSON(json['workCondition']),
        'kpi': json['kpi'] == null ? undefined : KpiSectionRequestFromJSON(json['kpi']),
        'odObject': json['odObject'] == null ? undefined : OdObjectRequestFromJSON(json['odObject']),
    };
}

export function JobDescriptionCreateRequestToJSON(json: any): JobDescriptionCreateRequest {
    return JobDescriptionCreateRequestToJSONTyped(json, false);
}

export function JobDescriptionCreateRequestToJSONTyped(value?: JobDescriptionCreateRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'title': value['title'],
        'jobDescriptionTemplateId': value['jobDescriptionTemplateId'],
        'validFrom': value['validFrom'] == null ? undefined : ((value['validFrom'] as any).toISOString()),
        'validTo': value['validTo'] == null ? undefined : ((value['validTo'] as any).toISOString()),
        'confidentialityLevel': ConfidentialityLevelTypeToJSON(value['confidentialityLevel']),
        'lastApprovedBy': value['lastApprovedBy'],
        'dimensions': DimensionsSectionRequestToJSON(value['dimensions']),
        'skillsManagement': SkillsManagementSectionRequestToJSON(value['skillsManagement']),
        'approvals': ApprovalsSectionRequestToJSON(value['approvals']),
        'purpose': PurposeSectionRequestToJSON(value['purpose']),
        'keyAccountabilities': KeyAccountabilitiesSectionRequestToJSON(value['keyAccountabilities']),
        'communicationAndWorkingRelationship': CommunicationAndWorkingRelationshipSectionRequestToJSON(value['communicationAndWorkingRelationship']),
        'workQualification': WorkQualificationSectionRequestToJSON(value['workQualification']),
        'workCondition': WorkConditionSectionRequestToJSON(value['workCondition']),
        'kpi': KpiSectionRequestToJSON(value['kpi']),
        'odObject': OdObjectRequestToJSON(value['odObject']),
    };
}

