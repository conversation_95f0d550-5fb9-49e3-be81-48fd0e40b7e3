import { FC, Children, ReactElement, cloneElement } from 'react';

import isEmpty from 'lodash/isEmpty';

import { useStyleConfig, useMaxIndex } from './hooks';
import { Surplus } from './Surplus';
import { AvatarSize, Max, Total } from './types';

import { UserProfileAvatarProps } from '../UserProfileAvatar';

interface AvatarGroupProps {
  children?: ReactElement<UserProfileAvatarProps>[];
  max?: Max;
  total?: Total;
  size?: AvatarSize;
}

export const AvatarGroup: FC<AvatarGroupProps> = ({
  children = [],
  size = AvatarSize.Medium,
  max = 2,
  total
}) => {
  const styleConfig = useStyleConfig(size);
  const maxIndex = useMaxIndex(max, total, children.length);
  const normalizedTotal = total || children.length;

  return (
    <div data-attributes="AvatarGroup" className="inline-flex">
      {Children.map(children, (child, index) => {
        if (index < maxIndex) {
          const clonedChild = cloneElement(child, {
            size: child.props.size ?? styleConfig.size
          });

          const wrapperProps = index > 0 && {
            style: { marginLeft: styleConfig.spacing }
          };

          return (
            <div data-attributes="AvatarGroup" {...wrapperProps}>
              {clonedChild}
            </div>
          );
        }
      })}
      {!isEmpty(children) && maxIndex < normalizedTotal && (
        <Surplus styleConfig={styleConfig} value={normalizedTotal - maxIndex} />
      )}
    </div>
  );
};
