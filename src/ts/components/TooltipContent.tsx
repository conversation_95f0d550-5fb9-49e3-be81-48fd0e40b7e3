import { FC, PropsWithChildren } from 'react';

import clsx from 'clsx';

import { TOOLTIP_MAX_LINES } from '@/contracts/tooltip';
import { generateLineClampStyle } from '@/utils';

import { Text } from './text';

interface TooltipContentProps extends PropsWithChildren {
  className?: string;
}

export const TooltipContent: FC<TooltipContentProps> = ({
  className,
  children
}) => (
  <Text
    className={clsx(
      'md:max-w-[268px] md:text-body-4-regular md:text-tooltip-text',
      className
    )}
    style={generateLineClampStyle(TOOLTIP_MAX_LINES)}
    as="p"
  >
    {children}
  </Text>
);
