import { FC, PropsWithChildren, useCallback, DragEvent, useRef } from 'react';

import clsx from 'clsx';

import { Icon, Text } from '@/components';

import { Divider } from './Divider';

import { DEFAULT_DISABLED } from '../constants';

interface UploadDragAndDropWrapperProps extends PropsWithChildren {
  disabled?: boolean;
  withFileList?: boolean;

  isDraggingFile: boolean;
  isOver: boolean;
  setIsOver: (isOver: boolean) => void;
  onChange: (e: DragEvent<HTMLDivElement>) => void;
}

export const UploadDragAndDropWrapper: FC<UploadDragAndDropWrapperProps> = ({
  children,

  disabled = DEFAULT_DISABLED,
  withFileList = false,

  isDraggingFile,
  isOver,
  setIsOver,
  onChange
}) => {
  const dragCounterRef = useRef(0);

  const handleDragEnter = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      if (disabled) return;

      e.preventDefault();
      dragCounterRef.current++;

      if (e.dataTransfer?.types?.includes('Files')) {
        setIsOver(true);
      }
    },
    [disabled, setIsOver]
  );

  const handleDragLeave = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      if (disabled) return;

      e.preventDefault();
      dragCounterRef.current--;

      if (dragCounterRef.current <= 0) {
        setIsOver(false);
      }
    },
    [disabled, setIsOver]
  );

  const handleDrop = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      if (disabled) return;

      e.preventDefault();
      dragCounterRef.current = 0;
      setIsOver(false);

      onChange(e);
    },
    [disabled, onChange, setIsOver]
  );

  let label: string | null;
  if (isDraggingFile) {
    label = isOver ? 'Drop your file here' : 'Upload File';
  } else {
    label = null;
  }

  return (
    <div data-attributes="UploadDragAndDropWrapper" className="flex flex-col">
      <div
        className={clsx('h-[72px]')}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {!disabled && label ? (
          <div
            className={clsx(
              'flex h-[72px] items-center justify-center gap-4 px-20 py-16'
            )}
          >
            <Icon
              name={isOver ? 'download' : 'upload'}
              width={18}
              height={18}
            />
            <Text className="text-body-1-medium text-text-heading">
              {label}
            </Text>
          </div>
        ) : (
          <div className="px-20 py-16">{children}</div>
        )}
      </div>

      {withFileList && <Divider className="mx-20" />}
    </div>
  );
};
