import { FC, useMemo, useState } from 'react';

import clsx from 'clsx';

import { Button, ButtonSize, ButtonVariant } from '@oh/components';

import { Icon } from '@/components';

import { DividerShower } from './DividerShower';
import { UploadedFileRow } from './UploadedFileRow';
import { UploadedGroupRow } from './UploadedGroupRow';

import { NEED_TOGGLE_LENGTH } from '../constants';
import {
  CustomFile,
  DefaultFileListInput,
  FileListInput,
  GroupFileInput,
  isGroupFileListInput
} from '../types';

interface UploadedFileListProps {
  fileList: DefaultFileListInput;
  viewOnly?: boolean;
  isDownloadBtnDisabled?: boolean;
  isRemoveBtnDisabled?: boolean;

  renderList?: (fileList: DefaultFileListInput) => FileListInput;
  onRemove: (
    file: CustomFile,
    event?: unknown
  ) => void | boolean | Promise<void | boolean>;
  onRetryUpload: (
    file: CustomFile,
    event?: unknown
  ) => void | boolean | Promise<void | boolean>;
  onDownload?: (file: CustomFile) => void;
}

export const UploadedFileList: FC<UploadedFileListProps> = ({
  fileList,
  viewOnly,
  isDownloadBtnDisabled,
  isRemoveBtnDisabled,

  renderList,
  onRemove,
  onRetryUpload,
  onDownload
}) => {
  const listToRender = useMemo(
    () => (renderList ? renderList(fileList) : fileList),
    [renderList, fileList]
  );

  const [isExpanded, setIsExpanded] = useState(false);
  const needToggle = listToRender.length > NEED_TOGGLE_LENGTH;
  const displayedList =
    needToggle && !isExpanded
      ? listToRender.slice(0, NEED_TOGGLE_LENGTH)
      : listToRender;

  const handleToggleExpanded = () => {
    setIsExpanded((prev) => !prev);
  };

  const isGrouped = isGroupFileListInput(displayedList);

  return (
    <div data-attributes="UploadedFileList" className="flex flex-col">
      <ul>
        {displayedList.map((entity, index) => {
          return (
            <li
              data-attributes="UploadedFileList"
              key={isGrouped ? index : (entity as CustomFile).id}
              className="flex flex-col"
            >
              {isGrouped ? (
                <UploadedGroupRow
                  group={entity as GroupFileInput}
                  viewOnly={viewOnly}
                  isDownloadBtnDisabled={isDownloadBtnDisabled}
                  isRemoveBtnDisabled={isRemoveBtnDisabled}
                  onRemove={onRemove}
                  onRetryUpload={onRetryUpload}
                  onDownload={onDownload}
                />
              ) : (
                <UploadedFileRow
                  file={entity as CustomFile}
                  viewOnly={viewOnly}
                  isDownloadBtnDisabled={isDownloadBtnDisabled}
                  isRemoveBtnDisabled={isRemoveBtnDisabled}
                  onRemove={onRemove}
                  onRetryUpload={onRetryUpload}
                  onDownload={onDownload}
                />
              )}

              <DividerShower total={listToRender.length} currentIndex={index} />
            </li>
          );
        })}
      </ul>

      {needToggle && (
        <div className="flex h-[56px] items-center justify-center">
          <Button
            variant={ButtonVariant.Link}
            size={ButtonSize.Small}
            className="flex items-center gap-4"
            onClick={handleToggleExpanded}
          >
            Show {isExpanded ? 'Less' : 'More'}
            <Icon
              name={'expandUp'}
              className={clsx('[&>path]:fill-current', {
                '!rotate-[180deg]': !isExpanded
              })}
              fill="currentColor"
            />
          </Button>
        </div>
      )}
    </div>
  );
};
