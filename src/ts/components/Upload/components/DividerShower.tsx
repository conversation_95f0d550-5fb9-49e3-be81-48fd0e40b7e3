import { FC } from 'react';

import { Divider } from './Divider';

import { NEED_TOGGLE_LENGTH } from '../constants';

interface DividerShowerProps {
  total: number;
  currentIndex: number;
}

export const DividerShower: FC<DividerShowerProps> = ({
  total,
  currentIndex
}) => {
  const divider = <Divider className="mx-20" />;

  return total - 1 !== currentIndex
    ? divider
    : total > NEED_TOGGLE_LENGTH && divider;
};
