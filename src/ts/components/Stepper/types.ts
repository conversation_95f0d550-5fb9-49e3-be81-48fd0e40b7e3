import { ReactNode } from 'react';

import { TypographyVariant } from '../typography';

export type StepperVariantsProp = 'sm' | 'lg';

export type StepperComponentProps = {
  steps: StepProps[];
  currentStep: number;
  circleVariant: StepperVariantsProp;
  dividerVariant: StepperVariantsProp;
  titleTextVariant: TypographyVariant;
  stepTextVariant: TypographyVariant;
  titleTextClassName: string;
  iconClassName?: string;
  circleClassName?: string;
  dividerClassName?: string;
};

export type StepProps = {
  title: string;
  step: number;
  [key: string]: string | number;
};

export type StepperDividerProps = {
  variant: StepperVariantsProp;
  className?: string;
};

export type StepperCircleProps = {
  children: ReactNode;
  variant: StepperVariantsProp;
  className?: string;
};
