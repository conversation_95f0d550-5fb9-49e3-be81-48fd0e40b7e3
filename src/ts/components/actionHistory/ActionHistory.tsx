import { FC, memo, ReactElement, useCallback, useMemo, useState } from 'react';

import { UseQueryResult } from '@tanstack/react-query';
import clsx from 'clsx';

import {
  ActionHistoryAppearance,
  Button,
  ButtonSize,
  ButtonVariant,
  Loader,
  ActionHistory as OHActionHistory
} from '@oh/components';
import { ActionHistoryItem } from '@oh/contracts/action-history';

import { useGetAvatarRequest } from '@/components/UserProfileAvatar';
import { useUserInfoMap } from '@/hooks';

import { ActionHistoryDrawer } from './components';

import { ActionHistoryOTItem } from '../approvalFlow';
import { ErrorScreen } from '../ErrorScreen';
import { Text } from '../text';

export type UserLoadMode = 'on' | 'silent' | 'off';

export const ActionHistory: FC<{
  historyRequest: UseQueryResult<ActionHistoryItem[], Error>;
  isApprovals?: boolean;
  containerClassName?: string;
  userLoadMode?: UserLoadMode;
}> = ({
  historyRequest,
  containerClassName,
  isApprovals = false,
  userLoadMode
}): ReactElement => {
  const { data, isPending, isError, error } = historyRequest;

  if (isPending) return <Loader />;
  if (isError) {
    return <ErrorScreen error={error} />;
  }

  return (
    <ActionHistoryBlockContent
      userLoadMode={userLoadMode}
      history={data}
      isApprovals={isApprovals}
      className={containerClassName}
    />
  );
};

export interface ActionHistoryBlockContentProps {
  history: ActionHistoryItem[];
  isApprovals?: boolean;
  className?: string;
  userLoadMode?: UserLoadMode;
}

const ActionHistoryBlockContent: FC<ActionHistoryBlockContentProps> = memo(
  ({ history, isApprovals, className, userLoadMode = 'on' }) => {
    const containerClassName = `onetalent-action-history [&>div>div>div>[data-attributes=action-status]]:flex
    [&>div>div>div>div>div>[data-attributes=approver-name]]:text-cta-1-medium
    [&>div>div>div>[data-attributes=action-status]]:flex-nowrap
    [&>div>div>div>[data-attributes=action-status]]:text-body-2-medium
    [&>div>div>div>[data-attributes=action-status]>svg]:flex-[1_0_auto]
    [&>div>div>div>[data-attributes=vertical-line]]:text-divider--secondary
    [&>div>div>div>[data-attributes=action-comment]]:whitespace-pre-wrap
    [&>div>[data-attributes=action-history-item]]:mb-4
    [&>div>[data-attributes=action-history-item]]:gap-x-16
    first:[&>div>[data-attributes=action-history-item]>div]:gap-y-4
    last:[&>div>[data-attributes=action-history-item]>div]:mb-20
    last:[&>div>[data-attributes=action-history-item]>div]:gap-y-12
    `;
    const [isOpenDrawer, setIsOpenDrawer] = useState(false);
    const getAvatarRequest = useGetAvatarRequest();

    const closeDrawer = useCallback(() => {
      setIsOpenDrawer(false);
    }, [setIsOpenDrawer]);

    const openDrawer = useCallback(() => {
      setIsOpenDrawer(true);
    }, [setIsOpenDrawer]);

    const userEmails = useMemo(
      () =>
        history
          .map((item) => item.fromEmail)
          .filter((s): s is string => Boolean(s)),
      [history]
    );

    const { data: usersMap = new Map(), isPending } = useUserInfoMap(
      userEmails,
      userLoadMode !== 'off'
    );

    const enhancedHistory = useMemo(() => {
      if (!usersMap) {
        return history;
      }

      return history.map((item: ActionHistoryOTItem) => {
        if (!item.fromEmail) {
          return {
            ...item,
            additionalInfo: item.delegatorName
              ? `On behalf of ${item.delegatorName}`
              : item.jobTitle
          };
        }

        const userInfo = usersMap.get(item.fromEmail);
        if (!userInfo) {
          return {
            ...item,
            additionalInfo: item.delegatorName
              ? `On behalf of ${item.delegatorName}`
              : item.jobTitle
          };
        }

        return {
          ...item,
          jobTitle: userInfo.jobTitle,
          fullName: userInfo.displayName,
          additionalInfo: userInfo.delegatorName
            ? `On behalf of ${userInfo.delegatorName}`
            : userInfo.jobTitle
        };
      });
    }, [history, usersMap]);

    if (isPending && userLoadMode === 'on') return <Loader />;

    return (
      <div
        data-attributes="ActionHistory"
        className={clsx(
          'rounded-xl bg-surface-grey_0 px-16',
          isApprovals ? 'my-16' : 'mb-40 mt-16',
          className
        )}
      >
        <div
          className={clsx('mb-16 py-16', {
            'border-b border-solid border-divider-light': isApprovals
          })}
        >
          <Text className="text-body-2-medium text-text-heading">
            Action History
          </Text>
        </div>
        <div>
          <OHActionHistory
            appearance={ActionHistoryAppearance.section}
            items={enhancedHistory}
            hasError={false}
            isLoading={false}
            getRequestFunction={getAvatarRequest}
            containerClassName={containerClassName}
          />
          <div className="flex justify-center border-t border-solid border-divider-light py-8">
            <Button
              size={ButtonSize.Small}
              variant={ButtonVariant.Link}
              onClick={openDrawer}
            >
              Show more
            </Button>
          </div>
          {isOpenDrawer && (
            <ActionHistoryDrawer
              history={enhancedHistory}
              closeDrawer={closeDrawer}
            />
          )}
        </div>
      </div>
    );
  }
);
