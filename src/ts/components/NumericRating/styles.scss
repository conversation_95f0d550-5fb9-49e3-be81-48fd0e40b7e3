$cell-border-radius: 4px;

$success-colors: (
  1: var(--numeric_rating-success-selected-fill-1),
  2: var(--numeric_rating-success-selected-fill-2),
  3: var(--numeric_rating-success-selected-fill-3),
  4: var(--numeric_rating-success-selected-fill-4),
  5: var(--numeric_rating-success-selected-fill-5)
);

$warning-colors: (
  1: var(--numeric_rating-warning-selected-fill-1),
  2: var(--numeric_rating-warning-selected-fill-2),
  3: var(--numeric_rating-warning-selected-fill-3),
  4: var(--numeric_rating-warning-selected-fill-4)
);

$outdated-colors: (
  1: var(--numeric_rating-outdated-fill-1),
  2: var(--numeric_rating-outdated-fill-2),
  3: var(--numeric_rating-outdated-fill-3),
  4: var(--numeric_rating-outdated-fill-4),
  5: var(--numeric_rating-outdated-fill-5)
);

.numeric-rating__cell {

  &:not(:last-child) {
    &::after {
      content: '';
      width: 4px;
      height: 100%;
      position: absolute;
      top: 0;
      right: -4px;
    }
  }
  
  &:nth-child(1 of .numeric-rating__cell) {
    border-top-left-radius: $cell-border-radius;
    border-bottom-left-radius: $cell-border-radius;
  }
  
  &:nth-last-child(1 of .numeric-rating__cell) {
    border-top-right-radius: $cell-border-radius;
    border-bottom-right-radius: $cell-border-radius;
  }
}

.numeric-rating__cell--default {
  color: var(--text-heading);
  background-color: var(--numeric_rating-rested-fill-all);
}

.numeric-rating__cell--disabled {
  color: var(--text-secondary);
  background-color: var(--numeric_rating-disabled-fill-all);
  border: 1px solid var(--numeric_rating-disabled-fill-stroke);
}

.numeric-rating__cell--success {
  color: var(--numeric_rating-success-text-text);

  @each $index, $color in $success-colors {
    &:nth-child(#{$index} of .numeric-rating__cell) {
      background-color: $color;
    }
  }

  &.numeric-rating__cell--success:hover {
    border: 1px solid var(--numeric_rating-success-hover-stroke-all);
    background-color: var(--numeric_rating-success-hover-fill-all);
    color: var(--text-heading);
  }

  &.numeric-rating__cell--success:active {
    background-color: var(--numeric_rating-success-pressed-fill-all);
    color: var(--numeric_rating-success-text-text);
  }
}

.numeric-rating__cell--warning {
  color: var(--numeric_rating-warning-text-text);

  @each $index, $color in $warning-colors {
    &:nth-child(#{$index} of .numeric-rating__cell) {
      background-color: $color;
    }
  }

  &.numeric-rating__cell--warning:hover {
    border: 1px solid var(--numeric_rating-warning-hover-stroke-all);
    background-color: var(--numeric_rating-warning-hover-fill-all);
    color: var(--text-heading);
  }

  &.numeric-rating__cell--warning:active {
    background-color: var(--numeric_rating-warning-pressed-fill-all);
    color: var(--numeric_rating-warning-text-text);
  }
}

.numeric-rating__cell--outdated {
  color: var(--numeric_rating-outdated-text-text);
  background-color: var(--numeric_rating-default-unselected-fill-rested);

  @each $index, $color in $outdated-colors {
    &:nth-child(#{$index} of .numeric-rating__cell) {
      background-color: $color;
    }
  }
}

