import './styles.base.scss';
import '../TreeDataTable/style.scss';

import {
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo
} from 'react';

import { DataTable, DataTableRow, Paginator } from '@epam/uui';
import {
  DataColumnProps,
  DataRowOptions,
  DataRowProps,
  DataSourceState,
  DataTableRowProps,
  useArrayDataSource
} from '@epam/uui-core';
import { UseQueryResult } from '@tanstack/react-query';
import clsx from 'clsx';
import isUndefined from 'lodash/isUndefined';

import { Loader } from '@oh/components';

import { PageModel } from '@/core/domainModel';

import { GenericDataSourceState } from './useDataSourceState';
import { NonSortable, createNonSortableSource } from './utils';

import { ErrorScreen } from '../ErrorScreen';
import { TestIdWrapper } from '../qa-automation';

export interface GenericTableProps<TItem, TId, TFilters = unknown> {
  dataSourceState: GenericDataSourceState<TId, TFilters>;
  setDataSourceState: Dispatch<
    SetStateAction<GenericDataSourceState<TId, TFilters>>
  >;
  queryResult: UseQueryResult<PageModel<TItem>>;
  columns: DataColumnProps<TItem>[];
  renderRowContent?: (item: TItem) => React.ReactNode;
  renderNoResults?: () => ReactNode;
  onRowClick?: (id: TId, props: DataRowProps<TItem, TId>) => void;
  getRowOptions?: (
    item: TItem,
    index?: number
  ) => DataRowOptions<NonSortable<TItem>, TId>;
  isFoldedByDefault?(
    item: NonSortable<TItem>,
    state: DataSourceState<unknown, TId>
  ): boolean;
  isSelectable?: boolean;
  showPagination?: boolean;
  styles?: {
    minHeight?: number | string;
  };
  dataAttributes?: string;
  classNames?: string;
  paginationContainerClassName?: string;
  paginationSize?: '24' | '30';
  onVisibleRowsCountChanged?: (visibleRows: number | undefined) => void;
}

export function GenericDataTable<TItem, TId, TFilters>({
  dataSourceState,
  setDataSourceState,
  queryResult,
  columns,
  onRowClick,
  getRowOptions: customGetRowOptions,
  isFoldedByDefault: customIsFoldedByDefault,
  renderRowContent,
  renderNoResults,
  styles,
  isSelectable = false,
  showPagination = true,
  dataAttributes,
  classNames,
  paginationContainerClassName,
  onVisibleRowsCountChanged,
  paginationSize = '30'
}: GenericTableProps<TItem, TId, TFilters>) {
  const {
    data: staleData,
    isFetching,
    isPending,
    isError,
    error
  } = queryResult;
  const data = isError || isFetching || isPending ? undefined : staleData;

  const { items, sortBy } = useMemo(
    () => createNonSortableSource(data?.items || []),
    [data?.items]
  );

  const onClick = useCallback(
    (props: DataRowProps<TItem, TId>) => onRowClick?.(props.id, props),
    [onRowClick]
  );

  const defaultGetRowOptions = () => ({
    checkbox: { isVisible: isSelectable },
    isSelectable: true,
    onClick
  });

  const getRowOptions = customGetRowOptions || defaultGetRowOptions;

  const defaultIsFoldedByDefault = () => false;
  const isFoldedByDefault = customIsFoldedByDefault || defaultIsFoldedByDefault;

  const dataSource = useArrayDataSource<NonSortable<TItem>, TId, unknown>(
    {
      getRowOptions,
      isFoldedByDefault,
      cascadeSelection: 'explicit',
      items,
      sortBy
    },
    [items]
  );
  const view = dataSource.useView(dataSourceState, setDataSourceState, {});
  const props = view.getListProps();

  useEffect(() => {
    onVisibleRowsCountChanged?.(props.rowsCount);
  }, [onVisibleRowsCountChanged, props.rowsCount]);

  const setTotalResults = useCallback(
    (totalResults: number | undefined) => {
      setDataSourceState((state) =>
        state.totalResults !== totalResults ? { ...state, totalResults } : state
      );
    },
    [setDataSourceState]
  );

  useEffect(() => {
    if (!data) {
      if (isError) {
        setTotalResults(undefined);
      }
      return;
    }

    const totalResults = data.page?.totalResults || 0;
    setTotalResults(totalResults);
  }, [data, isError, setTotalResults]);

  const page = dataSourceState.page;
  const totalResults = data?.page?.totalResults || dataSourceState.totalResults;
  const pageSize = dataSourceState.pageSize;
  const totalPages =
    totalResults && pageSize ? Math.ceil(totalResults / pageSize) : 0;

  const onPageValueChange = useCallback(
    (newPage: number) => {
      setDataSourceState((state) => ({ ...state, page: newPage }));
    },
    [setDataSourceState]
  );

  useEffect(() => {
    if (page && page > 1 && totalPages < page) {
      onPageValueChange(page - 1);
    }
  }, [page, onPageValueChange, totalPages]);

  const stateNode = useMemo(() => {
    if (isFetching || isPending) {
      return (
        <TestIdWrapper testId={dataAttributes && `${dataAttributes}Loader`}>
          <Loader diameter={40} className="!h-auto" />
        </TestIdWrapper>
      );
    }

    if (isError) {
      return <ErrorScreen error={error} className="!h-auto" />;
    }

    if (items.length === 0) {
      return renderNoResults?.();
    }

    return null;
  }, [
    error,
    isError,
    isFetching,
    isPending,
    items.length,
    renderNoResults,
    dataAttributes
  ]);

  const renderRow = useCallback(
    (props: DataTableRowProps<TItem, TId>): React.ReactNode => {
      const isExpanded = dataSourceState?.expanded?.[props.id as string];
      const handleRowClick = (props: DataRowProps<TItem, TId>) =>
        setDataSourceState((current) => ({
          ...current,
          expanded: {
            ...current.expanded,
            [props.id as string]: !current.expanded?.[props.id as string]
          }
        }));

      return (
        <div data-attributes="tableExpandableRow" aria-expanded={isExpanded}>
          <DataTableRow
            key={props.rowKey}
            {...props}
            indent={(props.indent ?? 0) + 1}
            onClick={handleRowClick}
            isFoldable={true}
            isFolded={!isExpanded}
            onFold={handleRowClick}
            borderBottom={Boolean(isExpanded || props.isLastChild ? 0 : 1)}
          />
          {isExpanded && renderRowContent && props.value && (
            <div
              className={clsx('row-content-wrapper', {
                last: props.isLastChild
              })}
            >
              {renderRowContent(props.value)}
            </div>
          )}
        </div>
      );
    },
    [dataSourceState?.expanded, setDataSourceState, renderRowContent]
  );

  const isNoContent = Boolean(stateNode);

  const renderNoResultsBlock = useCallback(() => null, []);

  const minHeight = isUndefined(styles?.minHeight) ? 676 : styles.minHeight;
  return (
    <div
      className={clsx(
        'generic-table tree-table relative z-0 flex flex-col justify-between',
        classNames,
        { 'table-with-custom-row': renderRowContent }
      )}
      data-attributes={dataAttributes}
      style={{ minHeight }}
    >
      <div className={isNoContent ? 'no-content' : ''}>
        <DataTable
          {...props}
          getRows={view.getVisibleRows}
          renderRow={renderRowContent && renderRow}
          columns={columns}
          value={dataSourceState}
          onValueChange={setDataSourceState}
          renderNoResultsBlock={renderNoResultsBlock}
          headerSize="36"
          columnsGap="24"
        />
      </div>
      {stateNode && (
        <div
          className={clsx(
            'flex flex-grow',
            (isError || !items.length) && 'items-center'
          )}
        >
          {stateNode}
        </div>
      )}
      {page !== undefined && !isError && totalPages > 1 && showPagination && (
        <div
          data-attributes="tablePagination"
          className={clsx('mb-40 mt-16', paginationContainerClassName)}
        >
          <Paginator
            totalPages={totalPages}
            value={page}
            onValueChange={onPageValueChange}
            size={paginationSize}
          />
        </div>
      )}
    </div>
  );
}
