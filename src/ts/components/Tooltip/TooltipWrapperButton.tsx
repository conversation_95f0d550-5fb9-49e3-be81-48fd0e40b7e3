import { useState, ReactNode, ReactElement, useMemo, useCallback } from 'react';

import clsx from 'clsx';

import { Tooltip } from '@oh/components';
import { TooltipVariant } from '@oh/constants';

import { BottomDrawerPlain } from '@/components/BottomDrawerPlain';
import { useBreakpointsContext } from '@/contexts';

import { Text } from '../text';

interface ChildrenProps {
  onTouchStart?: () => void;
}
export interface TooltipWrapperButtonProps {
  tooltipText: ReactNode | string;
  tooltipVariant?: TooltipVariant;
  tooltipClassName?: string;
  children: (props?: ChildrenProps) => ReactElement;
  title?: ReactNode;
}

export const TooltipWrapperButton = ({
  tooltipText,
  tooltipVariant = TooltipVariant.Dark,
  tooltipClassName,
  children,
  title
}: TooltipWrapperButtonProps) => {
  const { isMobile } = useBreakpointsContext();

  if (isMobile) {
    return (
      <TooltipWrapperButtonMobile title={title} tooltipText={tooltipText}>
        {children}
      </TooltipWrapperButtonMobile>
    );
  }

  return (
    <Tooltip
      title={tooltipText}
      variant={tooltipVariant}
      className={clsx(tooltipClassName)}
    >
      {children()}
    </Tooltip>
  );
};

type TooltipWrapperButtonMobileProps = Pick<
  TooltipWrapperButtonProps,
  'children' | 'tooltipText' | 'title'
>;

const TooltipWrapperButtonMobile = ({
  tooltipText,
  children,
  title
}: TooltipWrapperButtonMobileProps) => {
  const [isDrawerOpened, setIsDrawerOpened] = useState(false);

  const onTouchStart = useCallback(() => {
    tooltipText && setIsDrawerOpened(true);
  }, [tooltipText, setIsDrawerOpened]);

  const childrenProps = useMemo(
    () => ({
      onTouchStart
    }),
    [onTouchStart]
  );

  return (
    <>
      {children(childrenProps)}
      {isDrawerOpened && (
        <BottomDrawerPlain
          title={title}
          isOpen={true}
          shouldCloseOnOverlayClick={false}
          shouldCloseOnEsc={false}
          onClose={() => setIsDrawerOpened(false)}
          mainContentClassName="!min-h-0 !pb-[52px]"
        >
          <Text className="text-body-2-regular">{tooltipText}</Text>
        </BottomDrawerPlain>
      )}
    </>
  );
};
