import {
  FC,
  Fragment,
  memo,
  PropsWithChildren,
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState
} from 'react';

import { Loader } from '@ot/onetalent-ui-kit';
import { UseQueryResult } from '@tanstack/react-query';
import clsx from 'clsx';

import {
  Action,
  ActionProps,
  ApprovalActionHandler
} from '@/components/actions/Action';
import { ActionOverride } from '@/components/actions/ApprovalButton';
import { Button, ButtonVariant } from '@/components/button';
import { useBreakpointsContext } from '@/contexts';
import { useClickOutside } from '@/hooks';

import { ActionIds } from '../approvalFlow';
import { Icon } from '../icons';

export interface ActionModel {
  actionId: ActionIds;
  displayName: string;
  disabled?: boolean;
  withoutJustification?: boolean;
  withoutIcon?: boolean;
  dataAttributes?: string;
}

export type ActionsModel = (ActionModel & { message?: string })[];

export interface ActionsProps {
  actionHandler: ApprovalActionHandler;
  actionsResult: UseQueryResult<ActionsModel, Error>;
  actionsOverrides?: Partial<Record<ActionIds, ActionOverride>>;
  className?: string;
  containerClassName?: string;
  enableCancelModal?: boolean;
  tooltipClassName?: string;
  justificationLength?: number;
}

export const Actions: FC<PropsWithChildren<ActionsProps>> = memo(
  ({
    actionHandler,
    actionsResult,
    actionsOverrides,
    className,
    containerClassName,
    enableCancelModal,
    tooltipClassName,
    justificationLength
  }) => {
    const { isMobile } = useBreakpointsContext();
    const containerRef = useRef<HTMLDivElement>(null);

    const handleIntersectionEntries = useCallback<IntersectionObserverCallback>(
      (entries) => {
        for (const { target, isIntersecting } of entries) {
          const targetElement = target as HTMLDivElement;

          if (isIntersecting) {
            targetElement.style.visibility = 'visible';
            setHiddenActions(actionsResult.data?.slice(+targetElement.id + 1));
          } else {
            targetElement.style.visibility = 'hidden';
            setHiddenActions(actionsResult.data?.slice(+targetElement.id));
          }
        }
      },
      [actionsResult.data]
    );

    const [intersectionObserver, setIntersectionObserver] =
      useState<IntersectionObserver>();

    useEffect(() => {
      if (containerRef.current && actionsResult.isFetched) {
        setIntersectionObserver(
          new IntersectionObserver(handleIntersectionEntries, {
            root: containerRef.current,
            threshold: 1.0
          })
        );
      }
    }, [actionsResult, handleIntersectionEntries]);

    useLayoutEffect(() => {
      return () => {
        intersectionObserver?.disconnect();
      };
    }, [intersectionObserver]);

    const [isKebabActive, setIsKebabActive] = useState(false);
    const [hiddenActions, setHiddenActions] = useState<
      UseQueryResult<ActionsModel, Error>['data']
    >([]);

    const handleKebabClose = useCallback(() => setIsKebabActive(false), []);

    const handleKebabClick = useCallback(
      () => setIsKebabActive(!isKebabActive),
      [isKebabActive]
    );

    const wrapperRef = useClickOutside({
      onClick: handleKebabClose
    });

    if (actionsResult.isFetching) {
      return (
        <div className="flex h-full w-full">
          <Loader size={isMobile ? 'Small' : 'Medium'} />
        </div>
      );
    }

    if (actionsResult.isError || actionsResult.data?.length === 0) {
      return null;
    }

    return (
      <div
        data-attributes="Actions"
        className={clsx('sticky bottom-0 z-[3]', containerClassName)}
      >
        <div
          className={clsx(
            'flex justify-end gap-x-8 rounded-t-[20px] md:rounded-xl',
            'bg-surface-grey_0 px-20 py-16 md:mb-8 md:p-12',
            'shadow-action_bar',
            className
          )}
        >
          <div
            className="flex items-center gap-12 overflow-x-hidden"
            ref={containerRef}
          >
            {intersectionObserver
              ? actionsResult.data?.map(
                  (
                    {
                      actionId,
                      displayName,
                      message,
                      disabled,
                      withoutJustification,
                      withoutIcon,
                      dataAttributes
                    },
                    index
                  ) => {
                    return (
                      <CollapsibleAction
                        disabled={disabled}
                        key={actionId}
                        message={message}
                        actionHandler={actionHandler}
                        actionId={actionId}
                        displayName={displayName}
                        actionOverride={
                          actionId !== undefined
                            ? actionsOverrides?.[actionId as ActionIds]
                            : undefined
                        }
                        tooltipClassName={tooltipClassName}
                        id={String(index)}
                        observer={intersectionObserver}
                        withoutJustification={withoutJustification}
                        enableCancelModal={enableCancelModal}
                        withoutIcon={withoutIcon}
                        justificationLength={justificationLength}
                        dataAttributes={dataAttributes}
                      />
                    );
                  }
                )
              : null}
          </div>
          {hiddenActions?.length ? (
            <>
              <Button
                onClick={handleKebabClick}
                variant={
                  isKebabActive
                    ? ButtonVariant.Primary
                    : ButtonVariant.Secondary
                }
                className="shrink-0 basis-[40px]"
              >
                <Icon name="kebab" />
              </Button>

              <div
                ref={wrapperRef}
                onClick={() => setIsKebabActive(false)}
                className={`absolute bottom-[100%] right-12 w-160 rounded-l-lg rounded-r-lg bg-white p-8 shadow-[0_6px_16px_0_rgba(0,0,0,0.12)] ${
                  isKebabActive ? 'block' : 'hidden'
                }`}
              >
                {hiddenActions?.map(
                  (
                    {
                      actionId,
                      displayName,
                      disabled,
                      withoutJustification,
                      withoutIcon,
                      dataAttributes
                    },
                    index
                  ) => (
                    <Fragment key={actionId}>
                      <Action
                        actionHandler={(...args) => {
                          actionHandler(...args);
                          setIsKebabActive(false);
                        }}
                        actionId={actionId}
                        displayName={displayName}
                        isKebabItem
                        disabled={disabled}
                        tooltipText={''}
                        actionOverride={
                          actionId !== undefined
                            ? actionsOverrides?.[actionId as ActionIds]
                            : undefined
                        }
                        withoutJustification={withoutJustification}
                        enableCancelModal={enableCancelModal}
                        withoutIcon={withoutIcon}
                        justificationLength={justificationLength}
                        dataAttributes={dataAttributes}
                      />
                      {index !== hiddenActions.length - 1 && (
                        <div className="h-[1px] bg-main-neutral-30" />
                      )}
                    </Fragment>
                  )
                )}
              </div>
            </>
          ) : undefined}
        </div>
      </div>
    );
  }
);

const CollapsibleAction: FC<
  ActionProps & {
    message?: string;
    id: string;
    observer?: IntersectionObserver;
  }
> = ({ id, observer, message, ...buttonProps }) => {
  const ref = useRef<HTMLDivElement>(null);
  useLayoutEffect(() => {
    const button = ref.current;

    if (button && observer) {
      observer.observe(button);
    }
    return () => {
      if (button && observer) {
        observer.unobserve(button);
      }
    };
  }, [observer, ref]);
  return (
    <div data-attributes="Actions" className="shrink-0" id={id} ref={ref}>
      <Action
        tooltipText={message}
        tooltipClassName={clsx(
          'whitespace-pre-wrap !rounded !px-8 !py-4 text-body-4-regular',
          buttonProps.tooltipClassName
        )}
        {...buttonProps}
      />
    </div>
  );
};
