import { MouseEvent, useCallback } from 'react';

import clsx from 'clsx';

import { Dropdown, DropdownSkeletonProps } from '@/components/Dropdown';

import { SelectItem, SelectItemVariant } from '../select';
import { Text } from '../text';
export interface ContextMenuOption<
  T = unknown,
  <PERSON><PERSON><PERSON> extends string | number = string
> {
  key: TKey;
  title: string;
  tooltipText?: string;
  disabled?: boolean;
  onClick: (e: MouseEvent<HTMLButtonElement>, key: TKey, context?: T) => void;
}

export interface ContextMenuProps<
  T = unknown,
  T<PERSON><PERSON> extends string | number = string
> extends Omit<DropdownSkeletonProps, 'renderContent'> {
  options: ContextMenuOption<T, TKey>[];
  context?: T;
  optionVariant?: SelectItemVariant;
  optionRounded?: boolean;
  contentClassName?: string;
}

const Divider = () => (
  <div className="h-[1px] w-full border-b border-solid border-divider-mid" />
);

export function ContextMenu<
  T = unknown,
  <PERSON><PERSON><PERSON> extends string | number = string
>({
  options,
  renderChildren,
  context,
  optionVariant,
  optionRounded,
  contentClassName,
  ...dropdownProps
}: ContextMenuProps<T, TKey>) {
  const renderedContent = useCallback(
    (onClose?: () => void) => (
      <div
        className={clsx(
          'bg-dropdown-background shadow-dropdown',
          'rounded-lg',
          'max-w-360',
          'p-8',
          contentClassName
        )}
      >
        {options.map(
          ({ onClick, title, key, disabled, tooltipText }, index) => (
            <div key={key}>
              <SelectItem
                className={clsx({ '!rounded-none': !optionRounded })}
                onClick={(e) => {
                  e.stopPropagation();
                  onClick(e, key, context);
                  onClose?.();
                }}
                disabled={disabled}
                tooltipText={tooltipText}
                tooltipClassName="!z-[2700]"
                variant={optionVariant}
              >
                <Text className="text-body-2-regular">{title}</Text>
              </SelectItem>
              {options.length !== index + 1 && <Divider />}
            </div>
          )
        )}
      </div>
    ),
    [options, context, optionVariant, optionRounded, contentClassName]
  );
  return (
    <Dropdown
      {...dropdownProps}
      renderContent={renderedContent}
      renderChildren={renderChildren}
    />
  );
}
