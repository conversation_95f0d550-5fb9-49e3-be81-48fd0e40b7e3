import { ActionPopupWrapperProps } from './types';

import { ActionBar } from '../ActionBar';
import { BottomDrawerPlain } from '../BottomDrawerPlain';

export const ActionPopupWrapperMobile = ({
  isOpen,
  header,
  children,
  isFullHeight,
  renderFooter,
  onClose
}: ActionPopupWrapperProps) => (
  <BottomDrawerPlain
    isOpen={isOpen}
    title={header}
    isFullHeight={isFullHeight}
    shouldCloseOnOverlayClick={false}
    shouldCloseOnEsc={false}
    {...(renderFooter && {
      footer: (
        <ActionBar className="flex max-h-[104px] justify-end gap-x-16">
          {renderFooter()}
        </ActionBar>
      )
    })}
    onClose={onClose}
  >
    {children}
  </BottomDrawerPlain>
);
