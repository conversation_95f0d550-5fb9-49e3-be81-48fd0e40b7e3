import { useBreakpointsContext } from '@/contexts';

import { ActionPopupWrapperDesktop } from './ActionPopupWrapperDesktop';
import { ActionPopupWrapperMobile } from './ActionPopupWrapperMobile';
import { ActionPopupWrapperProps } from './types';

export const ActionPopupWrapper = (props: ActionPopupWrapperProps) => {
  const { isMobile } = useBreakpointsContext();

  return isMobile ? (
    <ActionPopupWrapperMobile {...props} />
  ) : (
    <ActionPopupWrapperDesktop {...props} />
  );
};
