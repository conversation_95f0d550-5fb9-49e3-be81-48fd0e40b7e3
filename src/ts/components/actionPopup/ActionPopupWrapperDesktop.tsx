import clsx from 'clsx';

import { Cross } from '@oh/assets/icons';
import { Modal, IconButton } from '@oh/components';

import { ActionPopupWrapperProps } from './types';

export const ActionPopupWrapperDesktop = ({
  isOpen,
  header,
  headerClassName,
  children,
  containerClassName,
  renderFooter,
  onClose
}: ActionPopupWrapperProps) => (
  <Modal
    appElement={document.getElementsByTagName('body')}
    isOpen={isOpen}
    shouldCloseOnEsc
    shouldCloseOnOverlayClick
    className="flex size-full items-center justify-center"
    overlayClassName="bg-[rgba(0,0,0,0.4)]"
  >
    <div
      className={clsx(
        'relative flex min-h-[300px] w-[600px] flex-col rounded-[16px] bg-surface-grey_10',
        containerClassName
      )}
    >
      <header className="flex h-72 items-center justify-between p-24">
        <h2 className={clsx('text-xl font-medium', headerClassName)}>
          {header}
        </h2>
        {onClose && (
          <IconButton
            icon={Cross}
            className="!rounded-8 !bg-surface-grey_0"
            size={40}
            iconClassName="!size-[26px] !text-modal-button-icon stroke-[0.5]"
            onClick={onClose}
          />
        )}
      </header>
      <main className="mb-64 h-full min-h-[164px] rounded-t-[16px] bg-surface-grey_0 p-20">
        {children}
      </main>
      {renderFooter && (
        <footer className="absolute bottom-0 flex h-64 w-full justify-end gap-x-16 rounded-xl bg-surface-grey_0 px-24 py-12 shadow-action_footer">
          {renderFooter()}
        </footer>
      )}
    </div>
  </Modal>
);
