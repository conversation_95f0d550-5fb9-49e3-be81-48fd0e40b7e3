import { FC, ReactElement, useEffect, useMemo } from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';

import clsx from 'clsx';

import { TextAreaValidated } from '@oh/components';

import { TextareaWithLimitType } from './types';

export const TextareaWithLimitDesktop: FC<TextareaWithLimitType> = ({
  label,
  name,
  maxLength = 300,
  rows = 2,
  rules,
  containerClassName = '',
  wrapperClassName = 'approvals-textarea',
  counterClassName = '',
  counterMode = 'always',
  maxHeight,
  trimBeforeCounting = false,
  isErrorMessageVisible = true,
  ...rest
}): ReactElement | null => {
  let textAreaElement: HTMLTextAreaElement | null = null;
  const { control, trigger } = useFormContext();

  const text = useWatch({ name });

  const watchTextarea = useWatch({ name });

  useEffect(() => {
    if (!watchTextarea) return;
    trigger(name);
  }, [trigger, name, watchTextarea]);

  useEffect(() => {
    if (!maxHeight) {
      return;
    }

    if (textAreaElement) {
      const textArea = textAreaElement;
      const isFocused = document.activeElement === textArea;
      const {
        lineHeight,
        paddingTop,
        paddingBottom,
        borderTopWidth,
        borderBottomWidth
      } = window.getComputedStyle(textArea);
      const [actualTextHeight, actualPadding, borderOffset] = [
        parseInt(lineHeight),
        parseInt(paddingTop) + parseInt(paddingBottom),
        parseInt(borderTopWidth) + parseInt(borderBottomWidth)
      ];
      const actualRowsHeight = rows * actualTextHeight + actualPadding;
      const isOnLimit = textArea.scrollHeight > maxHeight;

      if (!isFocused) {
        textArea.style.height = 'auto';
        if (textArea.scrollHeight > actualRowsHeight) {
          textArea.style.height = `${Math.min(
            textArea.scrollHeight + borderOffset,
            maxHeight
          )}px`;
          textArea.style.overflowY = !isOnLimit ? 'hidden' : 'scroll';
        }
      }

      const resizeTextArea = () => {
        textArea.style.height = 'auto';
        textArea.style.overflowY =
          textArea.scrollHeight < maxHeight ? 'hidden' : 'scroll';

        if (textArea.scrollHeight > actualRowsHeight) {
          textArea.style.height = `${
            maxHeight > textArea.scrollHeight
              ? textArea.scrollHeight + borderOffset
              : maxHeight
          }px`;
        }
      };

      textArea.addEventListener('input', resizeTextArea);

      return () => textArea.removeEventListener('input', resizeTextArea);
    }
  }, [maxHeight, textAreaElement, rows, text]);

  const textLength = (trimBeforeCounting ? text?.trim() : text)?.length || 0;

  const showCounter = useMemo(() => {
    switch (counterMode) {
      case 'always':
        return true;
      case 'limitReached':
        return textLength >= maxLength;
      case 'hidden':
        return false;
    }
  }, [counterMode, textLength, maxLength]);

  if (!control) return null;

  return (
    <div
      data-attributes="TextareaWithLimitDesktop"
      className={clsx(
        'textarea input-field input-field-next flex flex-col gap-8',
        !isErrorMessageVisible && "[&_[class^='message--']]:hidden",
        wrapperClassName
      )}
    >
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field, fieldState }) => {
          return (
            <>
              <TextAreaValidated
                spellCheck
                label={label}
                rows={rows}
                {...field}
                {...rest}
                maxLength={maxLength}
                className={clsx({
                  'input-field-error': fieldState.invalid
                })}
                errorMessage={Boolean(fieldState.error) as unknown as string}
                containerClassName={containerClassName}
                ref={(element) => {
                  textAreaElement = element;
                  field.ref(element);
                }}
              />{' '}
              <div className="flex !flex-row justify-between">
                {isErrorMessageVisible && fieldState.error && (
                  <span className="text-label-xs-regular text-main-error-60">
                    {fieldState.error.message}
                  </span>
                )}
                {showCounter && (
                  <span
                    className={clsx(
                      'ml-auto text-label-xs-regular',
                      counterClassName
                    )}
                  >
                    {textLength}/{maxLength}
                  </span>
                )}
              </div>
            </>
          );
        }}
      />
    </div>
  );
};
