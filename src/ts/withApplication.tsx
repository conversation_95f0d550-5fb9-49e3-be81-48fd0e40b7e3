import { FC } from 'react';

import { ToastProvider } from '@ot/onetalent-ui-kit';

import type { FederationApp } from '@oh/contracts';

import { AuthProvider } from './modules/auth';
import {
  AppProvider,
  AppConfigProvider,
  AppRouterProvider,
  AppInsightsProvider,
  AppQueryClientProvider
} from './providers';
import { OneHubFederatedApplicationProps } from './types';

export type FederationAppProps = FederationApp;

export const withApplication =
  (
    WrappedComponent: FC<OneHubFederatedApplicationProps>
  ): FC<FederationAppProps> =>
  (props) => (
    <AppConfigProvider>
      <AppInsightsProvider>
        <AppQueryClientProvider>
          <AppProvider {...props}>
            <AuthProvider>
              <ToastProvider>
                <AppRouterProvider>
                  <WrappedComponent {...props} />
                </AppRouterProvider>
              </ToastProvider>
            </AuthProvider>
          </AppProvider>
        </AppQueryClientProvider>
      </AppInsightsProvider>
    </AppConfigProvider>
  );
