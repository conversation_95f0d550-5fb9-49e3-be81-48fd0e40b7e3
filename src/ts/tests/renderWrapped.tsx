import { FC, PropsWithChildren, ReactElement } from 'react';

import { ToastProvider } from '@ot/onetalent-ui-kit';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render } from '@testing-library/react';

import { UuiContextProvider } from '@/providers';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 0
    }
  }
});

export const UiWrapper: FC<PropsWithChildren<unknown>> = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    <ToastProvider>
      <UuiContextProvider>{children}</UuiContextProvider>
    </ToastProvider>
  </QueryClientProvider>
);

export const renderWrapped = (ui: ReactElement) => {
  return render(<UiWrapper>{ui}</UiWrapper>);
};
