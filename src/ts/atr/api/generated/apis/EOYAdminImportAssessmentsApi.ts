/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ProblemDetails,
} from '../models/index';
import {
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
} from '../models/index';

export interface V1AssessmentsEndOfYearPerformanceFormsImportPostRequest {
    file: Blob;
}

export interface V1AssessmentsEndOfYearPerformanceFormsImportValidatePostRequest {
    file: Blob;
}

/**
 * 
 */
export class EOYAdminImportAssessmentsApi extends runtime.BaseAPI {

    /**
     * Imports employees performance forms for current performance cycle from file
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportPostRaw(requestParameters: V1AssessmentsEndOfYearPerformanceFormsImportPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['file'] == null) {
            throw new runtime.RequiredError(
                'file',
                'Required parameter "file" was null or undefined when calling v1AssessmentsEndOfYearPerformanceFormsImportPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const consumes: runtime.Consume[] = [
            { contentType: 'multipart/form-data' },
            { contentType: 'application/x-www-form-urlencoded' },
        ];
        // @ts-ignore: canConsumeForm may be unused
        const canConsumeForm = runtime.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): any };
        let useForm = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new URLSearchParams();
        }

        if (requestParameters['file'] != null) {
            formParams.append('file', requestParameters['file'] as any);
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/performance-forms/import`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: formParams,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Imports employees performance forms for current performance cycle from file
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportPost(requestParameters: V1AssessmentsEndOfYearPerformanceFormsImportPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1AssessmentsEndOfYearPerformanceFormsImportPostRaw(requestParameters, initOverrides);
    }

    /**
     * Uploads csv template for performance forms import
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportTemplatesCsvGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/performance-forms/import/templates/csv`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Uploads csv template for performance forms import
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportTemplatesCsvGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1AssessmentsEndOfYearPerformanceFormsImportTemplatesCsvGetRaw(initOverrides);
    }

    /**
     * Uploads xls template for performance forms import
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportTemplatesXlsGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/performance-forms/import/templates/xls`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Uploads xls template for performance forms import
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportTemplatesXlsGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1AssessmentsEndOfYearPerformanceFormsImportTemplatesXlsGetRaw(initOverrides);
    }

    /**
     * Validates employees performance forms file for current performance cycle
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportValidatePostRaw(requestParameters: V1AssessmentsEndOfYearPerformanceFormsImportValidatePostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['file'] == null) {
            throw new runtime.RequiredError(
                'file',
                'Required parameter "file" was null or undefined when calling v1AssessmentsEndOfYearPerformanceFormsImportValidatePost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const consumes: runtime.Consume[] = [
            { contentType: 'multipart/form-data' },
            { contentType: 'application/x-www-form-urlencoded' },
        ];
        // @ts-ignore: canConsumeForm may be unused
        const canConsumeForm = runtime.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): any };
        let useForm = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new URLSearchParams();
        }

        if (requestParameters['file'] != null) {
            formParams.append('file', requestParameters['file'] as any);
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/performance-forms/import/validate`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: formParams,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Validates employees performance forms file for current performance cycle
     */
    async v1AssessmentsEndOfYearPerformanceFormsImportValidatePost(requestParameters: V1AssessmentsEndOfYearPerformanceFormsImportValidatePostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1AssessmentsEndOfYearPerformanceFormsImportValidatePostRaw(requestParameters, initOverrides);
    }

}
