/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  EmployeeDto,
  EmployeeInfoDto,
  ProblemDetails,
} from '../models/index';
import {
    EmployeeDtoFromJSON,
    EmployeeDtoToJSON,
    EmployeeInfoDtoFromJSON,
    EmployeeInfoDtoToJSON,
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
} from '../models/index';

export interface V1EmployeesEmployeeIdGetRequest {
    employeeId: string;
}

/**
 * 
 */
export class EmployeesApi extends runtime.BaseAPI {

    /**
     * Gets employee by employee id
     */
    async v1EmployeesEmployeeIdGetRaw(requestParameters: V1EmployeesEmployeeIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeDto>> {
        if (requestParameters['employeeId'] == null) {
            throw new runtime.RequiredError(
                'employeeId',
                'Required parameter "employeeId" was null or undefined when calling v1EmployeesEmployeeIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/employees/{employeeId}`.replace(`{${"employeeId"}}`, encodeURIComponent(String(requestParameters['employeeId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeDtoFromJSON(jsonValue));
    }

    /**
     * Gets employee by employee id
     */
    async v1EmployeesEmployeeIdGet(requestParameters: V1EmployeesEmployeeIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeDto> {
        const response = await this.v1EmployeesEmployeeIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Gets employee for current user
     */
    async v1EmployeesMeEmployeeInfoGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeInfoDto>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/employees/me/employee-info`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeInfoDtoFromJSON(jsonValue));
    }

    /**
     * Gets employee for current user
     */
    async v1EmployeesMeEmployeeInfoGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeInfoDto> {
        const response = await this.v1EmployeesMeEmployeeInfoGetRaw(initOverrides);
        return await response.value();
    }

}
