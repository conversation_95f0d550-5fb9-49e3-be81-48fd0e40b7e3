/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  PerformActionOnAssessmentWorkflowRequestRequestBody,
  ProblemDetails,
  WorkflowActionResponse,
  WorkflowChainResponse,
  WorkflowHistoryResponse,
} from '../models/index';
import {
    PerformActionOnAssessmentWorkflowRequestRequestBodyFromJSON,
    PerformActionOnAssessmentWorkflowRequestRequestBodyToJSON,
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
    WorkflowActionResponseFromJSON,
    WorkflowActionResponseToJSON,
    WorkflowChainResponseFromJSON,
    WorkflowChainResponseToJSON,
    WorkflowHistoryResponseFromJSON,
    WorkflowHistoryResponseToJSON,
} from '../models/index';

export interface V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGetRequest {
    workflowId: string;
}

export interface V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPostRequest {
    workflowId: string;
    performActionOnAssessmentWorkflowRequestRequestBody: PerformActionOnAssessmentWorkflowRequestRequestBody;
}

export interface V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGetRequest {
    workflowId: string;
}

export interface V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGetRequest {
    workflowId: string;
}

export interface V1TempAssessmentsEndOfYearWorkflowsWorkflowIdPutRequest {
    workflowId: string;
    performActionOnAssessmentWorkflowRequestRequestBody: PerformActionOnAssessmentWorkflowRequestRequestBody;
}

/**
 * 
 */
export class EOYWorkflowsApi extends runtime.BaseAPI {

    /**
     * Gets workflow actions available for current user
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGetRaw(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<WorkflowActionResponse>>> {
        if (requestParameters['workflowId'] == null) {
            throw new runtime.RequiredError(
                'workflowId',
                'Required parameter "workflowId" was null or undefined when calling v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/workflows/{workflowId}/employees/me/actions`.replace(`{${"workflowId"}}`, encodeURIComponent(String(requestParameters['workflowId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(WorkflowActionResponseFromJSON));
    }

    /**
     * Gets workflow actions available for current user
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGet(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<WorkflowActionResponse>> {
        const response = await this.v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Performs action by Id on workflow
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPostRaw(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['workflowId'] == null) {
            throw new runtime.RequiredError(
                'workflowId',
                'Required parameter "workflowId" was null or undefined when calling v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPost().'
            );
        }

        if (requestParameters['performActionOnAssessmentWorkflowRequestRequestBody'] == null) {
            throw new runtime.RequiredError(
                'performActionOnAssessmentWorkflowRequestRequestBody',
                'Required parameter "performActionOnAssessmentWorkflowRequestRequestBody" was null or undefined when calling v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/workflows/{workflowId}/employees/me/actions`.replace(`{${"workflowId"}}`, encodeURIComponent(String(requestParameters['workflowId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: PerformActionOnAssessmentWorkflowRequestRequestBodyToJSON(requestParameters['performActionOnAssessmentWorkflowRequestRequestBody']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Performs action by Id on workflow
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPost(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeActionsPostRaw(requestParameters, initOverrides);
    }

    /**
     * Gets workflow model (chain)
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGetRaw(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<WorkflowChainResponse>>> {
        if (requestParameters['workflowId'] == null) {
            throw new runtime.RequiredError(
                'workflowId',
                'Required parameter "workflowId" was null or undefined when calling v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/workflows/{workflowId}/employees/me/chain`.replace(`{${"workflowId"}}`, encodeURIComponent(String(requestParameters['workflowId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(WorkflowChainResponseFromJSON));
    }

    /**
     * Gets workflow model (chain)
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGet(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<WorkflowChainResponse>> {
        const response = await this.v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeChainGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Gets history of actions performed on workflow
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGetRaw(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<WorkflowHistoryResponse>>> {
        if (requestParameters['workflowId'] == null) {
            throw new runtime.RequiredError(
                'workflowId',
                'Required parameter "workflowId" was null or undefined when calling v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/assessments-end-of-year/workflows/{workflowId}/employees/me/history`.replace(`{${"workflowId"}}`, encodeURIComponent(String(requestParameters['workflowId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(WorkflowHistoryResponseFromJSON));
    }

    /**
     * Gets history of actions performed on workflow
     */
    async v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGet(requestParameters: V1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<WorkflowHistoryResponse>> {
        const response = await this.v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeHistoryGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * ONLY NON PROD. Used to move only Waas workflow between steps
     * (Temp) Moves Waas workflow between steps
     */
    async v1TempAssessmentsEndOfYearWorkflowsWorkflowIdPutRaw(requestParameters: V1TempAssessmentsEndOfYearWorkflowsWorkflowIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['workflowId'] == null) {
            throw new runtime.RequiredError(
                'workflowId',
                'Required parameter "workflowId" was null or undefined when calling v1TempAssessmentsEndOfYearWorkflowsWorkflowIdPut().'
            );
        }

        if (requestParameters['performActionOnAssessmentWorkflowRequestRequestBody'] == null) {
            throw new runtime.RequiredError(
                'performActionOnAssessmentWorkflowRequestRequestBody',
                'Required parameter "performActionOnAssessmentWorkflowRequestRequestBody" was null or undefined when calling v1TempAssessmentsEndOfYearWorkflowsWorkflowIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/v1/temp/assessments-end-of-year/workflows/{workflowId}`.replace(`{${"workflowId"}}`, encodeURIComponent(String(requestParameters['workflowId']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: PerformActionOnAssessmentWorkflowRequestRequestBodyToJSON(requestParameters['performActionOnAssessmentWorkflowRequestRequestBody']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * ONLY NON PROD. Used to move only Waas workflow between steps
     * (Temp) Moves Waas workflow between steps
     */
    async v1TempAssessmentsEndOfYearWorkflowsWorkflowIdPut(requestParameters: V1TempAssessmentsEndOfYearWorkflowsWorkflowIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.v1TempAssessmentsEndOfYearWorkflowsWorkflowIdPutRaw(requestParameters, initOverrides);
    }

}
