/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PotentialRatingDetailsInfoResponse
 */
export interface PotentialRatingDetailsInfoResponse {
    /**
     * 
     * @type {string}
     * @memberof PotentialRatingDetailsInfoResponse
     */
    editedByEmail?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PotentialRatingDetailsInfoResponse
     */
    editedByFullName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PotentialRatingDetailsInfoResponse
     */
    originalScore?: number | null;
    /**
     * 
     * @type {number}
     * @memberof PotentialRatingDetailsInfoResponse
     */
    newScore: number;
    /**
     * 
     * @type {string}
     * @memberof PotentialRatingDetailsInfoResponse
     */
    potentialRatingComment: string;
}

/**
 * Check if a given object implements the PotentialRatingDetailsInfoResponse interface.
 */
export function instanceOfPotentialRatingDetailsInfoResponse(value: object): value is PotentialRatingDetailsInfoResponse {
    if (!('newScore' in value) || value['newScore'] === undefined) return false;
    if (!('potentialRatingComment' in value) || value['potentialRatingComment'] === undefined) return false;
    return true;
}

export function PotentialRatingDetailsInfoResponseFromJSON(json: any): PotentialRatingDetailsInfoResponse {
    return PotentialRatingDetailsInfoResponseFromJSONTyped(json, false);
}

export function PotentialRatingDetailsInfoResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): PotentialRatingDetailsInfoResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'editedByEmail': json['editedByEmail'] == null ? undefined : json['editedByEmail'],
        'editedByFullName': json['editedByFullName'] == null ? undefined : json['editedByFullName'],
        'originalScore': json['originalScore'] == null ? undefined : json['originalScore'],
        'newScore': json['newScore'],
        'potentialRatingComment': json['potentialRatingComment'],
    };
}

export function PotentialRatingDetailsInfoResponseToJSON(value?: PotentialRatingDetailsInfoResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'editedByEmail': value['editedByEmail'],
        'editedByFullName': value['editedByFullName'],
        'originalScore': value['originalScore'],
        'newScore': value['newScore'],
        'potentialRatingComment': value['potentialRatingComment'],
    };
}

