/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface NoneInboxInfoResponse
 */
export interface NoneInboxInfoResponse {
    /**
     * 
     * @type {string}
     * @memberof NoneInboxInfoResponse
     */
    assessmentId: string;
    /**
     * 
     * @type {string}
     * @memberof NoneInboxInfoResponse
     */
    employeeId: string;
}

/**
 * Check if a given object implements the NoneInboxInfoResponse interface.
 */
export function instanceOfNoneInboxInfoResponse(value: object): value is NoneInboxInfoResponse {
    if (!('assessmentId' in value) || value['assessmentId'] === undefined) return false;
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    return true;
}

export function NoneInboxInfoResponseFromJSON(json: any): NoneInboxInfoResponse {
    return NoneInboxInfoResponseFromJSONTyped(json, false);
}

export function NoneInboxInfoResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): NoneInboxInfoResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'assessmentId': json['assessmentId'],
        'employeeId': json['employeeId'],
    };
}

export function NoneInboxInfoResponseToJSON(value?: NoneInboxInfoResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'assessmentId': value['assessmentId'],
        'employeeId': value['employeeId'],
    };
}

