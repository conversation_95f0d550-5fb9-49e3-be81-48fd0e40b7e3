/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ManagerConfirmation } from './ManagerConfirmation';
import {
    ManagerConfirmationFromJSON,
    ManagerConfirmationFromJSONTyped,
    ManagerConfirmationToJSON,
} from './ManagerConfirmation';
import type { CompletionStatus } from './CompletionStatus';
import {
    CompletionStatusFromJSON,
    CompletionStatusFromJSONTyped,
    CompletionStatusToJSON,
} from './CompletionStatus';

/**
 * 
 * @export
 * @interface SubordinateAssessmentsSummary
 */
export interface SubordinateAssessmentsSummary {
    /**
     * 
     * @type {string}
     * @memberof SubordinateAssessmentsSummary
     */
    currentPerformanceCycleName: string;
    /**
     * 
     * @type {Date}
     * @memberof SubordinateAssessmentsSummary
     */
    prenormalizationPhaseEndDate?: Date | null;
    /**
     * 
     * @type {CompletionStatus}
     * @memberof SubordinateAssessmentsSummary
     */
    employeeSelfAssessments: CompletionStatus;
    /**
     * 
     * @type {CompletionStatus}
     * @memberof SubordinateAssessmentsSummary
     */
    employeeManagerAssessments: CompletionStatus;
    /**
     * 
     * @type {ManagerConfirmation}
     * @memberof SubordinateAssessmentsSummary
     */
    nineBoxPlottingConfirmation: ManagerConfirmation;
    /**
     * 
     * @type {ManagerConfirmation}
     * @memberof SubordinateAssessmentsSummary
     */
    performanceDistributionConfirmation: ManagerConfirmation;
    /**
     * 
     * @type {ManagerConfirmation}
     * @memberof SubordinateAssessmentsSummary
     */
    secondLineNineBoxPlottingConfirmation: ManagerConfirmation;
    /**
     * 
     * @type {ManagerConfirmation}
     * @memberof SubordinateAssessmentsSummary
     */
    secondLinePerformanceDistributionConfirmation: ManagerConfirmation;
    /**
     * 
     * @type {CompletionStatus}
     * @memberof SubordinateAssessmentsSummary
     */
    talentReviewTasks: CompletionStatus;
}

/**
 * Check if a given object implements the SubordinateAssessmentsSummary interface.
 */
export function instanceOfSubordinateAssessmentsSummary(value: object): value is SubordinateAssessmentsSummary {
    if (!('currentPerformanceCycleName' in value) || value['currentPerformanceCycleName'] === undefined) return false;
    if (!('employeeSelfAssessments' in value) || value['employeeSelfAssessments'] === undefined) return false;
    if (!('employeeManagerAssessments' in value) || value['employeeManagerAssessments'] === undefined) return false;
    if (!('nineBoxPlottingConfirmation' in value) || value['nineBoxPlottingConfirmation'] === undefined) return false;
    if (!('performanceDistributionConfirmation' in value) || value['performanceDistributionConfirmation'] === undefined) return false;
    if (!('secondLineNineBoxPlottingConfirmation' in value) || value['secondLineNineBoxPlottingConfirmation'] === undefined) return false;
    if (!('secondLinePerformanceDistributionConfirmation' in value) || value['secondLinePerformanceDistributionConfirmation'] === undefined) return false;
    if (!('talentReviewTasks' in value) || value['talentReviewTasks'] === undefined) return false;
    return true;
}

export function SubordinateAssessmentsSummaryFromJSON(json: any): SubordinateAssessmentsSummary {
    return SubordinateAssessmentsSummaryFromJSONTyped(json, false);
}

export function SubordinateAssessmentsSummaryFromJSONTyped(json: any, ignoreDiscriminator: boolean): SubordinateAssessmentsSummary {
    if (json == null) {
        return json;
    }
    return {
        
        'currentPerformanceCycleName': json['currentPerformanceCycleName'],
        'prenormalizationPhaseEndDate': json['prenormalizationPhaseEndDate'] == null ? undefined : (new Date(json['prenormalizationPhaseEndDate'])),
        'employeeSelfAssessments': CompletionStatusFromJSON(json['employeeSelfAssessments']),
        'employeeManagerAssessments': CompletionStatusFromJSON(json['employeeManagerAssessments']),
        'nineBoxPlottingConfirmation': ManagerConfirmationFromJSON(json['nineBoxPlottingConfirmation']),
        'performanceDistributionConfirmation': ManagerConfirmationFromJSON(json['performanceDistributionConfirmation']),
        'secondLineNineBoxPlottingConfirmation': ManagerConfirmationFromJSON(json['secondLineNineBoxPlottingConfirmation']),
        'secondLinePerformanceDistributionConfirmation': ManagerConfirmationFromJSON(json['secondLinePerformanceDistributionConfirmation']),
        'talentReviewTasks': CompletionStatusFromJSON(json['talentReviewTasks']),
    };
}

export function SubordinateAssessmentsSummaryToJSON(value?: SubordinateAssessmentsSummary | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'currentPerformanceCycleName': value['currentPerformanceCycleName'],
        'prenormalizationPhaseEndDate': value['prenormalizationPhaseEndDate'] == null ? undefined : ((value['prenormalizationPhaseEndDate'] as any).toISOString().substring(0,10)),
        'employeeSelfAssessments': CompletionStatusToJSON(value['employeeSelfAssessments']),
        'employeeManagerAssessments': CompletionStatusToJSON(value['employeeManagerAssessments']),
        'nineBoxPlottingConfirmation': ManagerConfirmationToJSON(value['nineBoxPlottingConfirmation']),
        'performanceDistributionConfirmation': ManagerConfirmationToJSON(value['performanceDistributionConfirmation']),
        'secondLineNineBoxPlottingConfirmation': ManagerConfirmationToJSON(value['secondLineNineBoxPlottingConfirmation']),
        'secondLinePerformanceDistributionConfirmation': ManagerConfirmationToJSON(value['secondLinePerformanceDistributionConfirmation']),
        'talentReviewTasks': CompletionStatusToJSON(value['talentReviewTasks']),
    };
}

