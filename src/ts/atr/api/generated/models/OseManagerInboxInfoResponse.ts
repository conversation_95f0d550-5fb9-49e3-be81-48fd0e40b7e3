/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SubmitInfo } from './SubmitInfo';
import {
    SubmitInfoFromJSON,
    SubmitInfoFromJSONTyped,
    SubmitInfoToJSON,
} from './SubmitInfo';

/**
 * 
 * @export
 * @interface OseManagerInboxInfoResponse
 */
export interface OseManagerInboxInfoResponse {
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    assessmentId: string;
    /**
     * 
     * @type {Date}
     * @memberof OseManagerInboxInfoResponse
     */
    selfAssessmentDueDate?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof OseManagerInboxInfoResponse
     */
    selfAssessmentIsCompleted: boolean;
    /**
     * 
     * @type {Date}
     * @memberof OseManagerInboxInfoResponse
     */
    selfAssessmentCompletionDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    selfAssessmentConclusion: string;
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    employeeId: string;
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    assigneeId?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof OseManagerInboxInfoResponse
     */
    isAllowedByPhase: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof OseManagerInboxInfoResponse
     */
    isAdditionalActionAllowed: boolean;
    /**
     * 
     * @type {SubmitInfo}
     * @memberof OseManagerInboxInfoResponse
     */
    selfAssessmentLastSubmittedInfo?: SubmitInfo;
    /**
     * 
     * @type {Date}
     * @memberof OseManagerInboxInfoResponse
     */
    managerAssessmentDueDate?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof OseManagerInboxInfoResponse
     */
    managerAssessmentIsCompleted: boolean;
    /**
     * 
     * @type {Date}
     * @memberof OseManagerInboxInfoResponse
     */
    managerAssessmentCompletionDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    managerAssessmentConclusion: string;
    /**
     * 
     * @type {number}
     * @memberof OseManagerInboxInfoResponse
     */
    previousAssessmentRating: number;
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    previousAssessmentResult: string;
    /**
     * 
     * @type {number}
     * @memberof OseManagerInboxInfoResponse
     */
    currentAssessmentRating: number;
    /**
     * 
     * @type {string}
     * @memberof OseManagerInboxInfoResponse
     */
    currentAssessmentResult: string;
    /**
     * 
     * @type {SubmitInfo}
     * @memberof OseManagerInboxInfoResponse
     */
    managerAssessmentLastSubmittedInfo?: SubmitInfo;
    /**
     * 
     * @type {number}
     * @memberof OseManagerInboxInfoResponse
     */
    assignedTasksNumber: number;
    /**
     * 
     * @type {number}
     * @memberof OseManagerInboxInfoResponse
     */
    completedTasksNumber: number;
    /**
     * 
     * @type {number}
     * @memberof OseManagerInboxInfoResponse
     */
    assignedMilestonesNumber: number;
    /**
     * 
     * @type {number}
     * @memberof OseManagerInboxInfoResponse
     */
    completedMilestonesNumber: number;
}

/**
 * Check if a given object implements the OseManagerInboxInfoResponse interface.
 */
export function instanceOfOseManagerInboxInfoResponse(value: object): value is OseManagerInboxInfoResponse {
    if (!('assessmentId' in value) || value['assessmentId'] === undefined) return false;
    if (!('selfAssessmentIsCompleted' in value) || value['selfAssessmentIsCompleted'] === undefined) return false;
    if (!('selfAssessmentConclusion' in value) || value['selfAssessmentConclusion'] === undefined) return false;
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    if (!('isAllowedByPhase' in value) || value['isAllowedByPhase'] === undefined) return false;
    if (!('isAdditionalActionAllowed' in value) || value['isAdditionalActionAllowed'] === undefined) return false;
    if (!('managerAssessmentIsCompleted' in value) || value['managerAssessmentIsCompleted'] === undefined) return false;
    if (!('managerAssessmentConclusion' in value) || value['managerAssessmentConclusion'] === undefined) return false;
    if (!('previousAssessmentRating' in value) || value['previousAssessmentRating'] === undefined) return false;
    if (!('previousAssessmentResult' in value) || value['previousAssessmentResult'] === undefined) return false;
    if (!('currentAssessmentRating' in value) || value['currentAssessmentRating'] === undefined) return false;
    if (!('currentAssessmentResult' in value) || value['currentAssessmentResult'] === undefined) return false;
    if (!('assignedTasksNumber' in value) || value['assignedTasksNumber'] === undefined) return false;
    if (!('completedTasksNumber' in value) || value['completedTasksNumber'] === undefined) return false;
    if (!('assignedMilestonesNumber' in value) || value['assignedMilestonesNumber'] === undefined) return false;
    if (!('completedMilestonesNumber' in value) || value['completedMilestonesNumber'] === undefined) return false;
    return true;
}

export function OseManagerInboxInfoResponseFromJSON(json: any): OseManagerInboxInfoResponse {
    return OseManagerInboxInfoResponseFromJSONTyped(json, false);
}

export function OseManagerInboxInfoResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): OseManagerInboxInfoResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'assessmentId': json['assessmentId'],
        'selfAssessmentDueDate': json['selfAssessmentDueDate'] == null ? undefined : (new Date(json['selfAssessmentDueDate'])),
        'selfAssessmentIsCompleted': json['selfAssessmentIsCompleted'],
        'selfAssessmentCompletionDate': json['selfAssessmentCompletionDate'] == null ? undefined : (new Date(json['selfAssessmentCompletionDate'])),
        'selfAssessmentConclusion': json['selfAssessmentConclusion'],
        'employeeId': json['employeeId'],
        'assigneeId': json['assigneeId'] == null ? undefined : json['assigneeId'],
        'isAllowedByPhase': json['isAllowedByPhase'],
        'isAdditionalActionAllowed': json['isAdditionalActionAllowed'],
        'selfAssessmentLastSubmittedInfo': json['selfAssessmentLastSubmittedInfo'] == null ? undefined : SubmitInfoFromJSON(json['selfAssessmentLastSubmittedInfo']),
        'managerAssessmentDueDate': json['managerAssessmentDueDate'] == null ? undefined : (new Date(json['managerAssessmentDueDate'])),
        'managerAssessmentIsCompleted': json['managerAssessmentIsCompleted'],
        'managerAssessmentCompletionDate': json['managerAssessmentCompletionDate'] == null ? undefined : (new Date(json['managerAssessmentCompletionDate'])),
        'managerAssessmentConclusion': json['managerAssessmentConclusion'],
        'previousAssessmentRating': json['previousAssessmentRating'],
        'previousAssessmentResult': json['previousAssessmentResult'],
        'currentAssessmentRating': json['currentAssessmentRating'],
        'currentAssessmentResult': json['currentAssessmentResult'],
        'managerAssessmentLastSubmittedInfo': json['managerAssessmentLastSubmittedInfo'] == null ? undefined : SubmitInfoFromJSON(json['managerAssessmentLastSubmittedInfo']),
        'assignedTasksNumber': json['assignedTasksNumber'],
        'completedTasksNumber': json['completedTasksNumber'],
        'assignedMilestonesNumber': json['assignedMilestonesNumber'],
        'completedMilestonesNumber': json['completedMilestonesNumber'],
    };
}

export function OseManagerInboxInfoResponseToJSON(value?: OseManagerInboxInfoResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'assessmentId': value['assessmentId'],
        'selfAssessmentDueDate': value['selfAssessmentDueDate'] == null ? undefined : ((value['selfAssessmentDueDate'] as any).toISOString().substring(0,10)),
        'selfAssessmentIsCompleted': value['selfAssessmentIsCompleted'],
        'selfAssessmentCompletionDate': value['selfAssessmentCompletionDate'] == null ? undefined : ((value['selfAssessmentCompletionDate'] as any).toISOString().substring(0,10)),
        'selfAssessmentConclusion': value['selfAssessmentConclusion'],
        'employeeId': value['employeeId'],
        'assigneeId': value['assigneeId'],
        'isAllowedByPhase': value['isAllowedByPhase'],
        'isAdditionalActionAllowed': value['isAdditionalActionAllowed'],
        'selfAssessmentLastSubmittedInfo': SubmitInfoToJSON(value['selfAssessmentLastSubmittedInfo']),
        'managerAssessmentDueDate': value['managerAssessmentDueDate'] == null ? undefined : ((value['managerAssessmentDueDate'] as any).toISOString().substring(0,10)),
        'managerAssessmentIsCompleted': value['managerAssessmentIsCompleted'],
        'managerAssessmentCompletionDate': value['managerAssessmentCompletionDate'] == null ? undefined : ((value['managerAssessmentCompletionDate'] as any).toISOString().substring(0,10)),
        'managerAssessmentConclusion': value['managerAssessmentConclusion'],
        'previousAssessmentRating': value['previousAssessmentRating'],
        'previousAssessmentResult': value['previousAssessmentResult'],
        'currentAssessmentRating': value['currentAssessmentRating'],
        'currentAssessmentResult': value['currentAssessmentResult'],
        'managerAssessmentLastSubmittedInfo': SubmitInfoToJSON(value['managerAssessmentLastSubmittedInfo']),
        'assignedTasksNumber': value['assignedTasksNumber'],
        'completedTasksNumber': value['completedTasksNumber'],
        'assignedMilestonesNumber': value['assignedMilestonesNumber'],
        'completedMilestonesNumber': value['completedMilestonesNumber'],
    };
}

