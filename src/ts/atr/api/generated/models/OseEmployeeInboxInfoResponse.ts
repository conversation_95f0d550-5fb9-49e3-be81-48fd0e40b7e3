/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SubmitInfo } from './SubmitInfo';
import {
    SubmitInfoFromJSON,
    SubmitInfoFromJSONTyped,
    SubmitInfoToJSON,
} from './SubmitInfo';

/**
 * 
 * @export
 * @interface OseEmployeeInboxInfoResponse
 */
export interface OseEmployeeInboxInfoResponse {
    /**
     * 
     * @type {string}
     * @memberof OseEmployeeInboxInfoResponse
     */
    assessmentId: string;
    /**
     * 
     * @type {Date}
     * @memberof OseEmployeeInboxInfoResponse
     */
    selfAssessmentDueDate?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof OseEmployeeInboxInfoResponse
     */
    selfAssessmentIsCompleted: boolean;
    /**
     * 
     * @type {Date}
     * @memberof OseEmployeeInboxInfoResponse
     */
    selfAssessmentCompletionDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof OseEmployeeInboxInfoResponse
     */
    selfAssessmentConclusion: string;
    /**
     * 
     * @type {string}
     * @memberof OseEmployeeInboxInfoResponse
     */
    employeeId: string;
    /**
     * 
     * @type {string}
     * @memberof OseEmployeeInboxInfoResponse
     */
    assigneeId?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof OseEmployeeInboxInfoResponse
     */
    isAllowedByPhase: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof OseEmployeeInboxInfoResponse
     */
    isAdditionalActionAllowed: boolean;
    /**
     * 
     * @type {SubmitInfo}
     * @memberof OseEmployeeInboxInfoResponse
     */
    selfAssessmentLastSubmittedInfo?: SubmitInfo;
    /**
     * 
     * @type {number}
     * @memberof OseEmployeeInboxInfoResponse
     */
    assignedTasksNumber: number;
    /**
     * 
     * @type {number}
     * @memberof OseEmployeeInboxInfoResponse
     */
    completedTasksNumber: number;
    /**
     * 
     * @type {number}
     * @memberof OseEmployeeInboxInfoResponse
     */
    assignedMilestonesNumber: number;
    /**
     * 
     * @type {number}
     * @memberof OseEmployeeInboxInfoResponse
     */
    completedMilestonesNumber: number;
}

/**
 * Check if a given object implements the OseEmployeeInboxInfoResponse interface.
 */
export function instanceOfOseEmployeeInboxInfoResponse(value: object): value is OseEmployeeInboxInfoResponse {
    if (!('assessmentId' in value) || value['assessmentId'] === undefined) return false;
    if (!('selfAssessmentIsCompleted' in value) || value['selfAssessmentIsCompleted'] === undefined) return false;
    if (!('selfAssessmentConclusion' in value) || value['selfAssessmentConclusion'] === undefined) return false;
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    if (!('isAllowedByPhase' in value) || value['isAllowedByPhase'] === undefined) return false;
    if (!('isAdditionalActionAllowed' in value) || value['isAdditionalActionAllowed'] === undefined) return false;
    if (!('assignedTasksNumber' in value) || value['assignedTasksNumber'] === undefined) return false;
    if (!('completedTasksNumber' in value) || value['completedTasksNumber'] === undefined) return false;
    if (!('assignedMilestonesNumber' in value) || value['assignedMilestonesNumber'] === undefined) return false;
    if (!('completedMilestonesNumber' in value) || value['completedMilestonesNumber'] === undefined) return false;
    return true;
}

export function OseEmployeeInboxInfoResponseFromJSON(json: any): OseEmployeeInboxInfoResponse {
    return OseEmployeeInboxInfoResponseFromJSONTyped(json, false);
}

export function OseEmployeeInboxInfoResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): OseEmployeeInboxInfoResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'assessmentId': json['assessmentId'],
        'selfAssessmentDueDate': json['selfAssessmentDueDate'] == null ? undefined : (new Date(json['selfAssessmentDueDate'])),
        'selfAssessmentIsCompleted': json['selfAssessmentIsCompleted'],
        'selfAssessmentCompletionDate': json['selfAssessmentCompletionDate'] == null ? undefined : (new Date(json['selfAssessmentCompletionDate'])),
        'selfAssessmentConclusion': json['selfAssessmentConclusion'],
        'employeeId': json['employeeId'],
        'assigneeId': json['assigneeId'] == null ? undefined : json['assigneeId'],
        'isAllowedByPhase': json['isAllowedByPhase'],
        'isAdditionalActionAllowed': json['isAdditionalActionAllowed'],
        'selfAssessmentLastSubmittedInfo': json['selfAssessmentLastSubmittedInfo'] == null ? undefined : SubmitInfoFromJSON(json['selfAssessmentLastSubmittedInfo']),
        'assignedTasksNumber': json['assignedTasksNumber'],
        'completedTasksNumber': json['completedTasksNumber'],
        'assignedMilestonesNumber': json['assignedMilestonesNumber'],
        'completedMilestonesNumber': json['completedMilestonesNumber'],
    };
}

export function OseEmployeeInboxInfoResponseToJSON(value?: OseEmployeeInboxInfoResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'assessmentId': value['assessmentId'],
        'selfAssessmentDueDate': value['selfAssessmentDueDate'] == null ? undefined : ((value['selfAssessmentDueDate'] as any).toISOString().substring(0,10)),
        'selfAssessmentIsCompleted': value['selfAssessmentIsCompleted'],
        'selfAssessmentCompletionDate': value['selfAssessmentCompletionDate'] == null ? undefined : ((value['selfAssessmentCompletionDate'] as any).toISOString().substring(0,10)),
        'selfAssessmentConclusion': value['selfAssessmentConclusion'],
        'employeeId': value['employeeId'],
        'assigneeId': value['assigneeId'],
        'isAllowedByPhase': value['isAllowedByPhase'],
        'isAdditionalActionAllowed': value['isAdditionalActionAllowed'],
        'selfAssessmentLastSubmittedInfo': SubmitInfoToJSON(value['selfAssessmentLastSubmittedInfo']),
        'assignedTasksNumber': value['assignedTasksNumber'],
        'completedTasksNumber': value['completedTasksNumber'],
        'assignedMilestonesNumber': value['assignedMilestonesNumber'],
        'completedMilestonesNumber': value['completedMilestonesNumber'],
    };
}

