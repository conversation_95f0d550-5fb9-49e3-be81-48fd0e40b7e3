/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ModifyRatingRequestRequestBody
 */
export interface ModifyRatingRequestRequestBody {
    /**
     * 
     * @type {string}
     * @memberof ModifyRatingRequestRequestBody
     */
    comment: string;
}

/**
 * Check if a given object implements the ModifyRatingRequestRequestBody interface.
 */
export function instanceOfModifyRatingRequestRequestBody(value: object): value is ModifyRatingRequestRequestBody {
    if (!('comment' in value) || value['comment'] === undefined) return false;
    return true;
}

export function ModifyRatingRequestRequestBodyFromJSON(json: any): ModifyRatingRequestRequestBody {
    return ModifyRatingRequestRequestBodyFromJSONTyped(json, false);
}

export function ModifyRatingRequestRequestBodyFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModifyRatingRequestRequestBody {
    if (json == null) {
        return json;
    }
    return {
        
        'comment': json['comment'],
    };
}

export function ModifyRatingRequestRequestBodyToJSON(value?: ModifyRatingRequestRequestBody | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'comment': value['comment'],
    };
}

