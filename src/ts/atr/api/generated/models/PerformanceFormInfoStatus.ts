/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const PerformanceFormInfoStatus = {
    Empty: 'empty',
    NotCompleted: 'notCompleted',
    InProgress: 'inProgress',
    Completed: 'completed'
} as const;
export type PerformanceFormInfoStatus = typeof PerformanceFormInfoStatus[keyof typeof PerformanceFormInfoStatus];


export function instanceOfPerformanceFormInfoStatus(value: any): boolean {
    for (const key in PerformanceFormInfoStatus) {
        if (Object.prototype.hasOwnProperty.call(PerformanceFormInfoStatus, key)) {
            if ((PerformanceFormInfoStatus as Record<string, PerformanceFormInfoStatus>)[key] === value) {
                return true;
            }
        }
    }
    return false;
}

export function PerformanceFormInfoStatusFromJSON(json: any): PerformanceFormInfoStatus {
    return PerformanceFormInfoStatusFromJSONTyped(json, false);
}

export function PerformanceFormInfoStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormInfoStatus {
    return json as PerformanceFormInfoStatus;
}

export function PerformanceFormInfoStatusToJSON(value?: PerformanceFormInfoStatus | null): any {
    return value as any;
}

