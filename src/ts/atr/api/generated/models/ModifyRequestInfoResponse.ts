/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ModifyRequestInfoResponse
 */
export interface ModifyRequestInfoResponse {
    /**
     * 
     * @type {string}
     * @memberof ModifyRequestInfoResponse
     */
    assessmentId: string;
    /**
     * 
     * @type {string}
     * @memberof ModifyRequestInfoResponse
     */
    employeeId: string;
    /**
     * 
     * @type {number}
     * @memberof ModifyRequestInfoResponse
     */
    currentAssessmentRating: number;
    /**
     * 
     * @type {string}
     * @memberof ModifyRequestInfoResponse
     */
    currentAssessmentResult: string;
    /**
     * 
     * @type {string}
     * @memberof ModifyRequestInfoResponse
     */
    comment: string;
}

/**
 * Check if a given object implements the ModifyRequestInfoResponse interface.
 */
export function instanceOfModifyRequestInfoResponse(value: object): value is ModifyRequestInfoResponse {
    if (!('assessmentId' in value) || value['assessmentId'] === undefined) return false;
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    if (!('currentAssessmentRating' in value) || value['currentAssessmentRating'] === undefined) return false;
    if (!('currentAssessmentResult' in value) || value['currentAssessmentResult'] === undefined) return false;
    if (!('comment' in value) || value['comment'] === undefined) return false;
    return true;
}

export function ModifyRequestInfoResponseFromJSON(json: any): ModifyRequestInfoResponse {
    return ModifyRequestInfoResponseFromJSONTyped(json, false);
}

export function ModifyRequestInfoResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModifyRequestInfoResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'assessmentId': json['assessmentId'],
        'employeeId': json['employeeId'],
        'currentAssessmentRating': json['currentAssessmentRating'],
        'currentAssessmentResult': json['currentAssessmentResult'],
        'comment': json['comment'],
    };
}

export function ModifyRequestInfoResponseToJSON(value?: ModifyRequestInfoResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'assessmentId': value['assessmentId'],
        'employeeId': value['employeeId'],
        'currentAssessmentRating': value['currentAssessmentRating'],
        'currentAssessmentResult': value['currentAssessmentResult'],
        'comment': value['comment'],
    };
}

