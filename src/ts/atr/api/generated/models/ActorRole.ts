/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const ActorRole = {
    Unknown: 'unknown',
    Employee: 'employee',
    AutoUser: 'autoUser',
    Admin: 'admin'
} as const;
export type ActorRole = typeof ActorRole[keyof typeof ActorRole];


export function instanceOfActorRole(value: any): boolean {
    for (const key in ActorRole) {
        if (Object.prototype.hasOwnProperty.call(ActorRole, key)) {
            if ((ActorRole as Record<string, ActorRole>)[key] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ActorRoleFromJSON(json: any): ActorRole {
    return ActorRoleFromJSONTyped(json, false);
}

export function ActorRoleFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActorR<PERSON> {
    return json as ActorRole;
}

export function ActorRoleToJSON(value?: ActorRole | null): any {
    return value as any;
}

