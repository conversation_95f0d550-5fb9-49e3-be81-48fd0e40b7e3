/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AtrService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.672
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const PerformanceFormSectionStatus = {
    NotStarted: 'notStarted',
    Current: 'current',
    Finished: 'finished'
} as const;
export type PerformanceFormSectionStatus = typeof PerformanceFormSectionStatus[keyof typeof PerformanceFormSectionStatus];


export function instanceOfPerformanceFormSectionStatus(value: any): boolean {
    for (const key in PerformanceFormSectionStatus) {
        if (Object.prototype.hasOwnProperty.call(PerformanceFormSectionStatus, key)) {
            if ((PerformanceFormSectionStatus as Record<string, PerformanceFormSectionStatus>)[key] === value) {
                return true;
            }
        }
    }
    return false;
}

export function PerformanceFormSectionStatusFromJSON(json: any): PerformanceFormSectionStatus {
    return PerformanceFormSectionStatusFromJSONTyped(json, false);
}

export function PerformanceFormSectionStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormSectionStatus {
    return json as PerformanceFormSectionStatus;
}

export function PerformanceFormSectionStatusToJSON(value?: PerformanceFormSectionStatus | null): any {
    return value as any;
}

