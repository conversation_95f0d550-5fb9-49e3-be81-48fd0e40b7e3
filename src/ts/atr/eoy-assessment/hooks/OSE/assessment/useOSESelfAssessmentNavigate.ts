import { useCallback } from 'react';

import {
  ATREntryPoint,
  EOYRoutes
} from '@/atr/eoy-assessment/shared/constants';

import { useAtrNavigate } from '../../useAtrNavigate';

export const useOSESelfAssessmentNavigate = () => {
  const navigate = useAtrNavigate();

  return useCallback(
    (assessmentId: string, entryPoint = ATREntryPoint.INBOX) =>
      navigate(
        `${EOYRoutes.SelfAssessmentOSE}?assessmentId=${assessmentId}&entryPoint=${entryPoint}`
      ),
    [navigate]
  );
};
