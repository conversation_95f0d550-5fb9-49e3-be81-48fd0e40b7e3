import { useCallback } from 'react';

import { AtrTemplateRenderingType } from '@/atr/eoy-assessment/domain/assessments-end-of-year/hooks/manager/managerTypes';
import { useICSelfCompletedAssessmentNavigate } from '@/atr/eoy-assessment/hooks/IC/completedAssessment/useICSelfCompletedAssessmentNavigate';
import { ATREntryPoint } from '@/atr/eoy-assessment/shared/constants';

import { useOSESelfCompletedAssessmentNavigate } from './OSE/completedAssessment/useOSESelfCompletedAssessmentNavigate';

export function useSelfCompletedAssessmentNavigate() {
  const navigateToICSelfCompletedAssessment =
    useICSelfCompletedAssessmentNavigate();
  const navigateToOSESelfCompletedAssessment =
    useOSESelfCompletedAssessmentNavigate();

  return useCallback(
    (
      assessmentId = '',
      renderingType: AtrTemplateRenderingType,
      entryPoint: ATREntryPoint = ATREntryPoint?.TALENT_JOURNEY
    ) => {
      const type = renderingType || AtrTemplateRenderingType.Unknown;
      switch (type) {
        case AtrTemplateRenderingType.Ose:
          return navigateToOSESelfCompletedAssessment(assessmentId, entryPoint);
        case AtrTemplateRenderingType.Ic:
          return navigateToICSelfCompletedAssessment(assessmentId, entryPoint);
        default:
          return;
      }
    },
    [navigateToICSelfCompletedAssessment, navigateToOSESelfCompletedAssessment]
  );
}
