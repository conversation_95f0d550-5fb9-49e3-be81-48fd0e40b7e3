import { createContext } from 'react';

import { IFederatedComponentOptions } from '@oh/contracts';
import type {
  AccessToken,
  FederatedModuleProps,
  FederationApp
} from '@oh/contracts';

interface InboxOptions {
  inboxItemData?: Record<string, string>;
}

interface IAppContext extends Partial<FederationApp> {
  appId: string;
  messageToHostFunction?: FederatedModuleProps['messageToHostFunction'];
  options?: Record<string, unknown> & IFederatedComponentOptions & InboxOptions;
  acquireToken?: (token: string | string[]) => Promise<AccessToken>;
}

export const AppContext = createContext<IAppContext>({
  appId: ''
});
