import { createContext } from 'react';

import { Permission } from '@/constants';

export interface IUserPermissionsContext {
  canCreateSkills?: boolean;
  canImportSkills?: boolean;
  isSkillAdmin?: boolean;
  isGCSkillAdmin?: boolean;
  isSuperAdmin?: boolean;
  hasUserProfile: boolean;
  hasAnyGroupAvailable: boolean;
  canReviewSkillSuggestions?: boolean;
  jdCanViewJobTitle?: boolean;
  jdCanViewPositionTitle?: boolean;
  jdCanViewStandardTitle?: boolean;
  jdCanViewStrictlyConfidential?: boolean;
  hasPermission: (permission: Permission) => boolean;
}

export const UserPermissionsContext = createContext<IUserPermissionsContext>({
  canCreateSkills: false,
  canImportSkills: false,
  isSkillAdmin: false,
  isSuperAdmin: false,
  hasUserProfile: false,
  hasAnyGroupAvailable: false,
  canReviewSkillSuggestions: false,
  jdCanViewJobTitle: false,
  jdCanViewPositionTitle: false,
  jdCanViewStandardTitle: false,
  jdCanViewStrictlyConfidential: false,
  hasPermission: () => false
});
