import { StatusCodes } from 'http-status-codes';

import { parseValidationErrorResponse } from '@/utils/parseResponse';

const createMockResponse = (
  body: { status: StatusCodes; detail: string },
  status: number
) => {
  return {
    json: jest.fn().mockResolvedValue(body),
    status
  } as unknown as Response;
};

describe('parseValidationErrorResponse', () => {
  it('returns false if there is no response in the error object', async () => {
    const error = { message: 'Some error' };
    const result = await parseValidationErrorResponse(error);
    expect(result).toBe(false);
  });

  it('returns parsed errors when response contains valid JSON', async () => {
    const mockResponse = createMockResponse(
      { status: StatusCodes.BAD_REQUEST, detail: '{"field":"value"}' },
      StatusCodes.BAD_REQUEST
    );
    const error = { response: mockResponse };

    const result = await parseValidationErrorResponse(error);

    expect(result).toEqual({
      status: StatusCodes.BAD_REQUEST,
      errors: { field: 'value' }
    });
  });

  it('returns false when json parsing fails', async () => {
    const mockResponse = createMockResponse(
      { status: StatusCodes.BAD_REQUEST, detail: 'Invalid JSON string' },
      StatusCodes.BAD_REQUEST
    );
    const error = { response: mockResponse };
    jest.spyOn(JSON, 'parse').mockImplementation(() => {
      throw new Error('Invalid JSON');
    });

    const result = await parseValidationErrorResponse(error);
    expect(result).toBe(false);
  });

  it('returns false on exceptions thrown during response processing', async () => {
    const error = {
      response: {
        json: jest.fn().mockRejectedValue(new Error('Network error'))
      }
    };

    const result = await parseValidationErrorResponse(error);
    expect(result).toBe(false);
  });
});
