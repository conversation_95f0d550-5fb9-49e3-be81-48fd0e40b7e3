import { toast as foundationToast } from '@oh/components';
import { MessageType } from '@oh/contracts';

import { Toast } from './toast';

jest.mock('@oh/components', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
    default: jest.fn()
  }
}));

const toastOptions = {
  messageTitle: undefined
};

describe('Toast', () => {
  const messageToHostMock = jest.fn();

  describe('Standalone Mode Functionality', () => {
    it('initializes and displays success toast correctly in standalone mode', () => {
      const newToast = new Toast(true);
      const testMessage = 'Test successful';
      newToast.success(testMessage);

      expect(foundationToast.success).toHaveBeenCalledWith(
        testMessage,
        toastOptions
      );
    });

    it('handles error toasts correctly in standalone mode', () => {
      const testMessage = 'Error Occurred';
      const newToast = new Toast(true);
      newToast.error(testMessage);
      expect(foundationToast.error).toHaveBeenCalledWith(
        testMessage,
        toastOptions
      );
    });
  });

  describe('Federated Mode Functionality', () => {
    it('sends the correct message when displaying success toast', () => {
      const toast = new Toast(false, messageToHostMock);

      const testMessage = 'Federated success';

      toast.success(testMessage);
      expect(messageToHostMock).toHaveBeenCalledWith({
        type: MessageType.ToasterShow,
        payload: expect.objectContaining({
          status: 'SUCCESS',
          message: testMessage,
          externalId: ''
        })
      });
    });
  });

  describe('Error Handling in Federated Mode', () => {
    it('throws error when message to host function is not initialized', () => {
      const toast = new Toast(false, undefined);

      expect(() => {
        toast.error('Test Error Handling');
      }).toThrowError('messageToHostFunction was not provided');
    });
  });
  describe('Standalone Mode Functionality', () => {
    it('handles warning toasts correctly in standalone mode', () => {
      const testMessage = 'Warning Occurred';
      const newToast = new Toast(true);
      newToast.warning(testMessage);
      expect(foundationToast.warning).toHaveBeenCalledWith(
        testMessage,
        toastOptions
      );
    });

    it('handles info toasts correctly in standalone mode', () => {
      const testMessage = 'Info Message';
      const newToast = new Toast(true);
      newToast.info(testMessage);
      expect(foundationToast.info).toHaveBeenCalledWith(
        testMessage,
        toastOptions
      );
    });

    it('handles default toasts correctly in standalone mode', () => {
      const testMessage = 'Default Message';
      const newToast = new Toast(true);
      newToast.default(testMessage);
      expect(foundationToast.default).toHaveBeenCalledWith(
        testMessage,
        toastOptions
      );
    });
  });

  describe('Federated Mode Functionality', () => {
    it('sends the correct message when displaying error toast', () => {
      const toast = new Toast(false, messageToHostMock);

      const testMessage = 'Federated error';

      toast.error(testMessage);
      expect(messageToHostMock).toHaveBeenCalledWith({
        type: MessageType.ToasterShow,
        payload: expect.objectContaining({
          status: 'ERROR',
          message: testMessage,
          externalId: ''
        })
      });
    });

    it('sends the correct message when displaying warning toast', () => {
      const toast = new Toast(false, messageToHostMock);

      const testMessage = 'Federated warning';

      toast.warning(testMessage);
      expect(messageToHostMock).toHaveBeenCalledWith({
        type: MessageType.ToasterShow,
        payload: expect.objectContaining({
          status: 'WARNING',
          message: testMessage,
          externalId: ''
        })
      });
    });

    it('sends the correct message when displaying info toast', () => {
      const toast = new Toast(false, messageToHostMock);

      const testMessage = 'Federated info';

      toast.info(testMessage);
      expect(messageToHostMock).toHaveBeenCalledWith({
        type: MessageType.ToasterShow,
        payload: expect.objectContaining({
          status: 'INFO',
          message: testMessage,
          externalId: ''
        })
      });
    });

    it('sends the correct message when displaying default toast', () => {
      const toast = new Toast(false, messageToHostMock);

      const testMessage = 'Federated default';

      toast.default(testMessage);
      expect(messageToHostMock).toHaveBeenCalledWith({
        type: MessageType.ToasterShow,
        payload: expect.objectContaining({
          status: 'DEFAULT',
          message: testMessage,
          externalId: ''
        })
      });
    });
  });
});
