import { useCallback, useMemo } from 'react';
import { createSearchParams, useLocation, useNavigate } from 'react-router-dom';

import { VisibilityEntity } from '@/constants';
import { VisibilityManagerState } from '@/contracts/visibilityManager';

interface UseToggleVisibilityReturn {
  toggleDrawer: (params?: VisibilityManagerState) => void;
  toggleModal: (params?: VisibilityManagerState) => void;
}

export const useToggleVisibility = (): UseToggleVisibilityReturn => {
  const navigate = useNavigate();
  const { state } = useLocation();

  const toggleVisibility = useCallback(
    (params: VisibilityManagerState, entity: VisibilityEntity) => {
      const searchParams = new URLSearchParams(location.search);
      const data = { show: true, ...params };
      const name = data.name;
      const clearState = data.clearState ?? false;
      const entityParam = searchParams.get(entity);

      if (name && data.show && (!entityParam || entityParam !== name)) {
        searchParams.set(entity, String(name));
        Object.entries(params).forEach(([key, value]) => {
          if (key !== 'name') {
            searchParams.set(key, String(value));
          }
        });
      } else {
        searchParams.delete(entity);

        Object.keys(params).forEach((key) => {
          if (key !== 'name') {
            searchParams.delete(key);
          }
        });
      }

      navigate(
        { search: String(createSearchParams(searchParams)) },
        { state: clearState ? null : state, replace: true }
      );
    },
    [navigate, state]
  );

  const toggleDrawer = useCallback(
    (params: VisibilityManagerState = { show: false }) => {
      toggleVisibility(params, VisibilityEntity.Drawer);
    },
    [toggleVisibility]
  );

  const toggleModal = useCallback(
    (params: VisibilityManagerState = { show: false }) => {
      toggleVisibility(params, VisibilityEntity.Modal);
    },
    [toggleVisibility]
  );

  return useMemo(
    () => ({ toggleDrawer, toggleModal }),
    [toggleDrawer, toggleModal]
  );
};
