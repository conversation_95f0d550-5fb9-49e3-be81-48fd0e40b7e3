import { FC, lazy, useMemo } from 'react';

import { ErrorScreen } from '@/components/ErrorScreen';
import type { INonIndexRouteObject } from '@/contracts/router';
import { ObjectivesApprovalProps } from '@/performance/objectives/components';

const ObjectivesApprovals = lazy<FC<ObjectivesApprovalProps>>(async () => ({
  default: (await import('../../../pages/ObjectivesApprovalsPage'))
    .ObjectivesApprovalsPage
}));

export interface UseApprovalsRoutesProps {
  entityType?: string;
  requestId?: string;
  externalId?: string;
}

export function useApprovalsRoutes({
  entityType,
  requestId,
  externalId
}: UseApprovalsRoutesProps) {
  return useMemo(() => {
    if (!requestId || !entityType || !externalId) {
      return [errorRoute];
    }

    if (entityType === 'Objective') {
      return getObjectivesApprovalsRoutes(requestId, externalId);
    }

    return [errorRoute];
  }, [entityType, externalId, requestId]);
}

const getObjectivesApprovalsRoutes = (
  requestId: string,
  externalId: string
): INonIndexRouteObject[] => [
  {
    path: '/',
    element: (
      <ObjectivesApprovals requestId={requestId} externalId={externalId} />
    )
  },
  errorRoute
];

const errorRoute: INonIndexRouteObject = {
  path: '*',
  element: <ErrorScreen className="!h-auto" />
};
