import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useToastContext } from '@/contexts';
import { logger } from '@/logger';
import { V1ManagersMeSubordinatesSubordinateIdObjectivesObjectiveIdDeleteRequest } from '@/performance/api/generated';
import { usePerformanceServiceApi } from '@/performance/domain';

import { OBJECTIVES_REQUEST_KEYS } from './ObjectivesRequestKeys';

export const useDeleteObjectiveRequestByManager = () => {
  const { managersApi } = usePerformanceServiceApi();

  return useMutation({
    mutationFn: async ({
      subordinateId,
      objectiveId
    }: V1ManagersMeSubordinatesSubordinateIdObjectivesObjectiveIdDeleteRequest) => {
      try {
        return await managersApi.v1ManagersMeSubordinatesSubordinateIdObjectivesObjectiveIdDelete(
          {
            subordinateId,
            objectiveId
          }
        );
      } catch (error) {
        logger.error('Failed to post v1EmployeesMeObjectivesIdDelete', error);

        throw error;
      }
    }
  });
};

export const useDeleteObjectiveByManager = () => {
  const queryClient = useQueryClient();
  const { mutate, isPending } = useDeleteObjectiveRequestByManager();
  const { toast } = useToastContext();

  const deleteObjectiveByManager = (
    subordinateId: string,
    objectiveId: string,
    onSuccess?: () => void,
    onError?: () => void
  ) => {
    return mutate(
      { subordinateId, objectiveId },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: OBJECTIVES_REQUEST_KEYS.subordinateObjectives({
              subordinateId
            })
          });
          queryClient.invalidateQueries({
            queryKey: OBJECTIVES_REQUEST_KEYS.subordinateObjectivesOverview({
              subordinateId
            })
          });
          onSuccess && onSuccess();
          toast.success('Objective was successfully deleted');
        },
        onError: () => {
          onError && onError();
          toast.error('An error occurred, objective was not deleted');
        }
      }
    );
  };

  return { deleteObjectiveByManager, isPending };
};
