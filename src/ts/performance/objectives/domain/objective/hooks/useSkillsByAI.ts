import { useCallback } from 'react';

import { BasicObjectiveDetails } from '@performance/objectives/domain';
import { transformToObjectiveRequestModel } from '@performance/objectives/domain/objective/transformers';
import { useQuery } from '@tanstack/react-query';

import { logger } from '@/logger';
import { usePerformanceServiceApi } from '@/performance/domain/api';

import { OBJECTIVES_REQUEST_KEYS } from './ObjectivesRequestKeys';

function usePostSkillsByAI(
  getBasicObjectiveDetails: () => BasicObjectiveDetails
) {
  const { employeesApi } = usePerformanceServiceApi();

  const objective = transformToObjectiveRequestModel(
    getBasicObjectiveDetails()
  );

  return useCallback(async () => {
    try {
      return await employeesApi.v1EmployeesMeSkillsAiGeneratePost({
        aISearchSkilslRequest: {
          objective
        }
      });
    } catch (error) {
      logger.error('Failed to load v1EmployeesMeSkillsAiGeneratePost', error);

      throw error;
    }
  }, [employeesApi, objective]);
}

export function useSkillsByAI(
  getBasicObjectiveDetails: () => BasicObjectiveDetails
) {
  const getSkillsBy = usePostSkillsByAI(getBasicObjectiveDetails);

  return useQuery({
    queryKey: OBJECTIVES_REQUEST_KEYS.aiGeneratedSkills(),
    queryFn: () => getSkillsBy()
  });
}
