import { useCallback } from 'react';

import { useCurrentEmployeeContext } from '@performance/contexts';
import { OBJECTIVES_REQUEST_KEYS } from '@performance/objectives/domain/objective/hooks/ObjectivesRequestKeys';
import { useQuery } from '@tanstack/react-query';

import { logger } from '@/logger';
import { usePerformanceServiceApi } from '@/performance/domain/api/usePerformanceServiceApi';

const useGetManagerObjectives = () => {
  const { employeesApi } = usePerformanceServiceApi();

  return useCallback(async () => {
    try {
      return employeesApi.v1EmployeesMeManagersObjectivesGet();
    } catch (error) {
      logger.error('Failed to load v1EmployeesMeManagersObjectivesGet', error);

      throw error;
    }
  }, [employeesApi]);
};

export const useManagerObjectives = () => {
  const queryFn = useGetManagerObjectives();
  const { employee } = useCurrentEmployeeContext();

  return useQuery({
    queryKey: OBJECTIVES_REQUEST_KEYS.getManagerObjectives(employee.employeeId),
    queryFn
  });
};
