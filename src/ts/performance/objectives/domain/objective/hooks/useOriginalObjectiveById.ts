import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { logger } from '@/logger';
import { PerformanceService } from '@/performance/api';
import { usePerformanceServiceApi } from '@/performance/domain/api';

import { OBJECTIVES_REQUEST_KEYS } from './ObjectivesRequestKeys';

type Input = PerformanceService.V1EmployeesMeObjectivesIdOriginalGetRequest;

function useGetOriginalObjectiveById() {
  const { employeesApi } = usePerformanceServiceApi();

  return useCallback(
    async (input: Input) => {
      try {
        return await employeesApi.v1EmployeesMeObjectivesIdOriginalGet(input);
      } catch (error) {
        logger.error(
          'Failed to load v1EmployeesMeObjectivesIdOriginalGet',
          error
        );

        throw error;
      }
    },
    [employeesApi]
  );
}

export function useOriginalObjectiveById(input: Input) {
  const getOriginalObjectiveById = useGetOriginalObjectiveById();

  return useQuery({
    queryKey: OBJECTIVES_REQUEST_KEYS.originalObjective(input),
    queryFn: () => getOriginalObjectiveById(input)
  });
}
