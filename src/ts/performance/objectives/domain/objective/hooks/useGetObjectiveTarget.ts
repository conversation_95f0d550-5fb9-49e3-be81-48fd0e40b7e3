import { useTargetTypes } from '@performance/objectives/domain';
import { formatTargetDescription } from '@performance/objectives/domain/objective/utils';

interface Options {
  targetType: string;
  targetValue: number;
}

export const useGetObjectiveTarget = (objective: Options) => {
  const { data: targets } = useTargetTypes();
  const target = targets?.find((target) => target.id === objective.targetType);

  const targetDescription = formatTargetDescription({
    ...target,
    targetValue: objective.targetValue
  });
  return { targetDescription };
};
