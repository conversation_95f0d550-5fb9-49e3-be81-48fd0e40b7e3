import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { logger } from '@/logger';
import { usePerformanceSettings } from '@/performance/domain';
import { usePerformanceServiceApi } from '@/performance/domain/api';

import { OBJECTIVES_REQUEST_KEYS } from './ObjectivesRequestKeys';

function useGetMyObjectivesOverview() {
  const { employeesApi } = usePerformanceServiceApi();

  return useCallback(async () => {
    try {
      return employeesApi.v1EmployeesMeObjectivesOverviewGet();
    } catch (error) {
      logger.error('Failed to load v1EmployeesMeObjectivesOverviewGet', error);

      throw error;
    }
  }, [employeesApi]);
}

export function useMyObjectivesOverview() {
  const { isSuccess } = usePerformanceSettings();
  const getMyObjectives = useGetMyObjectivesOverview();

  return useQuery({
    queryKey: OBJECTIVES_REQUEST_KEYS.myObjectivesOverview(),
    queryFn: () => getMyObjectives(),
    enabled: isSuccess
  });
}
