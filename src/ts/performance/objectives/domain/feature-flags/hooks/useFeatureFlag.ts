import { FeatureFlags } from '@performance/objectives/domain';
import { useSuspenseQuery } from '@tanstack/react-query';

import { STALE_TIME } from '@/constants';
import { logger } from '@/logger';
import { usePerformanceServiceApi } from '@/performance/domain/api';

import { SHARED_FEATURES_REQUEST_KEYS } from './sharedFeaturesRequestKeys';

type FeatureMap = Partial<Record<FeatureFlags, boolean>>;

export function useFeatures() {
  const { sharedApi } = usePerformanceServiceApi();

  return useSuspenseQuery({
    queryKey: SHARED_FEATURES_REQUEST_KEYS.sharedFeatures(),
    queryFn: async () => {
      try {
        const features = (await sharedApi.v1SharedFeaturesGet()).features ?? {};

        return Object.entries(features).reduce<FeatureMap>(
          (acc, [name, { enabled }]) => ({ ...acc, [name]: enabled }),
          {}
        );
      } catch (error) {
        logger.error('Failed to load v1SharedFeaturesGet', error);

        throw error;
      }
    },
    staleTime: STALE_TIME.SESSION
  });
}

export function useFeatureFlag(featureName: FeatureFlags) {
  const queryResult = useFeatures();
  const isEnabled = queryResult.data[featureName] ?? false;

  return isEnabled;
}
