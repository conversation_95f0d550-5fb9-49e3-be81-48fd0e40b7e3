import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { logger } from '@/logger';
import { usePerformanceServiceApi } from '@/performance/domain/api';

import { transformDataResponseToCategories } from '../transformers';

export const CATEGORY_REQUEST_KEYS = {
  all: () => ['categories'] as const
} as const;

export const useGetCategories = () => {
  const { objectivesApi } = usePerformanceServiceApi();

  return useCallback(async () => {
    try {
      const data = await objectivesApi.v1ObjectivesTypesGet();

      return transformDataResponseToCategories(data);
    } catch (error) {
      logger.error('Failed to load v1ObjectivesTypesGet', error);

      throw error;
    }
  }, [objectivesApi]);
};

export const useCategories = () => {
  const queryFn = useGetCategories();

  return useQuery({
    queryKey: CATEGORY_REQUEST_KEYS.all(),
    queryFn: () => queryFn()
  });
};
