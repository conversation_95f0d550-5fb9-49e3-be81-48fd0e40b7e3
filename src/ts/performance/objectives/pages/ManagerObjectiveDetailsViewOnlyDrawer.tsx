import { FC, useCallback } from 'react';

import { ObjectiveDetailsViewOnly } from '@performance/objectives/components/ObjectiveDetails';
import { ObjectiveDetailsDrawer } from '@performance/objectives/pages/ObjectiveDetailsDrawer';

import { Loader } from '@oh/components';
import { useGetParameter } from '@oh/hooks';

import {
  EmptyStateScreen,
  FullScreenParamState,
  isExpandParamExist
} from '@/components';
import { Drawer } from '@/constants';
import { useToggleVisibility } from '@/hooks';

import { useManagerObjectiveById } from '../domain';

export const ManagerObjectiveDetailsViewOnlyDrawer: FC = () => {
  const objectiveId = useGetParameter('objectiveId');
  const sharedWithObjectiveId = useGetParameter('sharedWithObjectiveId');

  const {
    data: objective,
    isLoading,
    isError
  } = useManagerObjectiveById(objectiveId || '');

  const { toggleDrawer } = useToggleVisibility();

  const handleBackToSharedWith = useCallback(() => {
    toggleDrawer({
      name: Drawer.ObjectiveSharedWithViewOnly,
      expand: isExpandParamExist() === FullScreenParamState.Expanded,
      objectiveId: sharedWithObjectiveId,
      show: true
    });
  }, [sharedWithObjectiveId, toggleDrawer]);

  if (!objectiveId) return;
  return (
    <ObjectiveDetailsDrawer
      drawer={Drawer.ManagerObjectiveDetailsViewOnlyDrawer}
      withFullScreen
      onBack={sharedWithObjectiveId ? handleBackToSharedWith : undefined}
    >
      {isLoading ? (
        <Loader />
      ) : isError || !objective ? (
        <EmptyStateScreen
          title="Sorry, an error has occurred"
          subtitle="We can’t find the page you are looking for"
        />
      ) : (
        <ObjectiveDetailsViewOnly objective={objective} />
      )}
    </ObjectiveDetailsDrawer>
  );
};
