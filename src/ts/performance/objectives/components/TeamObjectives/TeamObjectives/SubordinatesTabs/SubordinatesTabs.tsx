import { FC, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { Tabs } from '@oh/components';

import { useSubordinatesTabs } from './hooks';
import { TabId } from './types';

export interface SubordinatesTabsProps {
  tabId: TabId;
  sharedObjectiveTotal?: number;
}

export const SubordinatesTabs: FC<SubordinatesTabsProps> = ({
  tabId,
  sharedObjectiveTotal
}) => {
  const navigate = useNavigate();
  const tabs = useSubordinatesTabs(sharedObjectiveTotal);

  const onChange = useCallback(
    (tabId: TabId) => {
      switch (tabId) {
        case TabId.Overview:
          navigate('/team/objectives');
          break;
        case TabId.SharedObjectives:
          navigate('/team/objectives/shared');
          break;
        default: {
          const _: never = tabId;
          throw 'Not all cases are covered.';
        }
      }
    },
    [navigate]
  );

  return (
    <Tabs
      activeTabId={tabId}
      items={tabs}
      className="mb-24 flex w-full flex-wrap border-b border-solid border-divider--secondary"
      onClick={onChange}
    />
  );
};
