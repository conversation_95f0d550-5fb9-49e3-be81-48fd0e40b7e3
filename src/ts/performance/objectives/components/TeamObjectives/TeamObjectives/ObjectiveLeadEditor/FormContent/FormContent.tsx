import { FC } from 'react';

import { Loader } from '@oh/components';

import { EmptyStateScreen } from '@/components';
import { SubordinateModel } from '@/performance/objectives/domain';

import { FieldSection } from './FieldSection';

interface FormContentProps {
  subordinates?: SubordinateModel[];
  isLoading: boolean;
  isError: boolean;
}

export const FormContent: FC<FormContentProps> = ({
  subordinates = [],
  isLoading,
  isError
}) => {
  if (isLoading) {
    return <Loader />;
  }

  if (isError) {
    return (
      <EmptyStateScreen
        title="Sorry, an error has occurred"
        subtitle="We can’t find the page you are looking for"
      />
    );
  }

  return <FieldSection subordinates={subordinates} disabled={isLoading} />;
};
