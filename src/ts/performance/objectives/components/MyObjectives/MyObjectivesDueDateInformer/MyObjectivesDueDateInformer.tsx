import { FC } from 'react';

import { useMyObjectivesOverview } from '@performance/objectives/domain';

import { Informer, InformerVariant } from '@/components';

import { useCalculateObjectivesByStatus } from './hooks';

export const MyObjectivesDueDateInformer: FC = () => {
  const {
    data: { totalWeight = 0, objectives = 0, objectivesCountByStatus } = {},
    isLoading
  } = useMyObjectivesOverview();

  const totalObjectivesByStatus = useCalculateObjectivesByStatus(
    objectivesCountByStatus
  );

  if (
    isLoading ||
    (totalWeight === 100 && objectives === totalObjectivesByStatus)
  ) {
    return null;
  }

  return (
    <Informer
      className="mb-16"
      variant={InformerVariant.Warning}
      title="Please Complete Your Objective Setting by March 31, 2025"
      // NOTE: IOS doesn't support MMMM DD, YYYY date format, it will be shown as Invalid date.
      // title={`Please Complete Your Objective Setting by ${dayjs(
      //   // TODO: Get a date from the ATR template when this feature is implemented. For more details, see: https://devopsad.visualstudio.com/OneTalent/_workitems/edit/303020
      //   '03-31-2025'
      // ).format('MMMM DD, YYYY')}`}
    />
  );
};
