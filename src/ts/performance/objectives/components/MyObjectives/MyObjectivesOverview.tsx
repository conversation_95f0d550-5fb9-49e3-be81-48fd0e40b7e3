import { ButtonVariant } from '@/components';
import {
  CascadeObjectiveButton,
  CreateObjectiveButton,
  ObjectivesHeader
} from '@/performance/components';
import { useCurrentEmployeeContext } from '@/performance/contexts';

export const MyObjectivesOverview = () => {
  const {
    employee: { isManager }
  } = useCurrentEmployeeContext();

  return (
    <ObjectivesHeader title="My Objectives">
      {isManager && (
        <CascadeObjectiveButton
          className="!ml-auto"
          variant={ButtonVariant.Secondary}
        />
      )}
      <CreateObjectiveButton />
    </ObjectivesHeader>
  );
};
