import { useCallback, useEffect, useMemo, useState } from 'react';

import {
  ObjectiveTag,
  RelatedObjectiveModel
} from '@performance/objectives/domain';
import isEmpty from 'lodash/isEmpty';

import { countWorkItems } from '../utils';

type ExpandedSectionsState = Record<string, boolean>;

export const useExpandSections = (
  sections: RelatedObjectiveModel[],
  expanded = false
) => {
  const [areAllSectionsExpanded, setAreAllSectionsExpanded] =
    useState<boolean>(expanded);

  const initialState = useMemo(() => {
    return sections.reduce<ExpandedSectionsState>(
      (
        acc,
        { employee: { employeeId }, milestones, tasks, tags, title, id }
      ) => {
        const uniqueId = `${id}_${employeeId}`;
        return id &&
          (countWorkItems({ milestones, tasks }) ||
            (title && tags.includes(ObjectiveTag.Aligned)))
          ? { ...acc, [uniqueId]: expanded }
          : acc;
      },
      {}
    );
  }, [expanded, sections]);

  const [expandedSections, setExpandedSections] =
    useState<ExpandedSectionsState>(initialState);

  const isSectionExpandedById = useCallback(
    (id: string) => expandedSections[id],
    [expandedSections]
  );

  const expandSectionById = useCallback((id: string) => {
    setExpandedSections((expandedSections) => ({
      ...expandedSections,
      [id]: !expandedSections[id]
    }));
  }, []);

  const expandAllSections = useCallback(() => {
    setExpandedSections((expandedSections) =>
      Object.keys(expandedSections).reduce<ExpandedSectionsState>(
        (acc, key) => ({ ...acc, [key]: !areAllSectionsExpanded }),
        {}
      )
    );
  }, [areAllSectionsExpanded]);

  useEffect(() => {
    if (Object.values(expandedSections).every((expanded) => expanded)) {
      setAreAllSectionsExpanded(true);
      return;
    }

    if (Object.values(expandedSections).every((expanded) => !expanded)) {
      setAreAllSectionsExpanded(false);
    }
  }, [expandedSections]);

  return {
    expandSectionById,
    expandAllSections,
    isSectionExpandedById,
    areAllSectionsExpanded,
    hasSectionsToExpand: !isEmpty(expandedSections)
  };
};

export type UseExpandSections = ReturnType<typeof useExpandSections>;
