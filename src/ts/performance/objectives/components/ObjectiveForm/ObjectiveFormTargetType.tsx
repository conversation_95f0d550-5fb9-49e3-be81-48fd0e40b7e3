import { Controller, useFormContext } from 'react-hook-form';

import { Loader } from '@oh/components';

import { AutoCompleteInput, AutoCompleteInputMobile } from '@/components';
import { useBreakpointsContext } from '@/contexts';

import {
  transformTargetTypesToSingleSelectOptions,
  useTargetTypes
} from '../../domain';

export const ObjectiveFormTargetType = () => {
  const { control } = useFormContext();
  const { data, isLoading } = useTargetTypes();
  const options = transformTargetTypesToSingleSelectOptions(data);
  const { isMobile } = useBreakpointsContext();

  return (
    <Controller
      control={control}
      name="targetType"
      render={({ field }) => {
        const WrapperComp = isMobile
          ? AutoCompleteInputMobile
          : AutoCompleteInput;
        if (isLoading) return <Loader />;

        return (
          <WrapperComp
            {...field}
            value={field.value}
            label="Target Type *"
            placeholder="Select"
            suggestions={options}
            id="objectiveFormTargetTypes"
          />
        );
      }}
    />
  );
};
