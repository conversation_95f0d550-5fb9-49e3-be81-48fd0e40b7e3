import { FC } from 'react';

import { Informer, InformerVariant } from '@/components';
import {
  useCurrentPerformanceCycle,
  usePerformanceSettings
} from '@/performance/domain';

import { useIsMaximumObjectivesAmountReached } from '../../domain';

export const SelectObjectivesMaxAmountInformer: FC = () => {
  const currentCycle = useCurrentPerformanceCycle();

  const { data: settings } = usePerformanceSettings();

  const { isMaximumObjectivesAmountReached } =
    useIsMaximumObjectivesAmountReached();

  if (isMaximumObjectivesAmountReached) {
    return null;
  }

  const maxObjectiveAmount = settings?.maxObjectiveAmount ?? 0;

  const title = `Maximum number of objectives ${maxObjectiveAmount}`;
  const description = `Please note that the maximum number of the objectives you can setup for ${currentCycle?.name} is limited to ${maxObjectiveAmount} objectives`;

  return (
    <Informer
      className="mb-16 md:mb-24"
      variant={InformerVariant.Generic}
      title={title}
      description={description}
    />
  );
};
