/* eslint-disable @typescript-eslint/no-explicit-any */

import { FC, PropsWithChildren, useReducer } from 'react';

import {
  CreateObjectivesContext,
  CreateObjectivesDispatchContext,
  createObjectivesInitialState,
  createObjectivesReducer
} from '../../domain';

type CreateObjectivesContextProviderProviderProps = PropsWithChildren;

export const CreateObjectivesContextProvider: FC<
  CreateObjectivesContextProviderProviderProps
> = ({ children }) => {
  const [state, dispatch] = useReducer(
    createObjectivesReducer,
    createObjectivesInitialState
  );

  return (
    <CreateObjectivesContext.Provider value={state}>
      <CreateObjectivesDispatchContext.Provider value={dispatch}>
        {children}
      </CreateObjectivesDispatchContext.Provider>
    </CreateObjectivesContext.Provider>
  );
};
