import { usePerformanceSettings } from '@performance/domain';
import dayjs from 'dayjs';

import { Tag } from '@oh/components';

import { Text } from '@/components';

interface NewObjectiveTagProps {
  createdDate: Date | string | null | undefined;
}
export function NewObjectiveTag({ createdDate }: NewObjectiveTagProps) {
  const { data: settings } = usePerformanceSettings();

  if (!settings) {
    return false;
  }
  const dayWhenTagExpires = dayjs(createdDate).add(
    settings.daysSinceObjectiveCreation,
    'day'
  );
  const showNewTag = dayWhenTagExpires.isAfter(dayjs());

  if (!showNewTag) return null;

  return (
    <Tag className="!inline-flex !bg-tag-light_blue-fill text-cta-2-regular !text-tag-light_blue-text">
      <Text className="text-cta-2-regular">New</Text>
    </Tag>
  );
}
