import { FC, useState, useCallback } from 'react';

import { ExpandableDrawer } from '@/components';

interface ObjectiveDetailsInnerDrawerProps {
  drawerTitle: string;
  renderInside: (closeDrawer: () => void) => React.ReactNode;
  renderOpener: (openDrawer: () => void) => React.ReactNode;
}

export const ObjectiveDetailsInnerDrawer: FC<
  ObjectiveDetailsInnerDrawerProps
> = ({ renderInside, drawerTitle, renderOpener }) => {
  const [isOpen, setIsOpen] = useState(false);

  const openDrawer = useCallback(() => setIsOpen(true), []);
  const closeDrawer = useCallback(() => setIsOpen(false), []);

  return (
    <>
      {renderOpener(openDrawer)}
      <ExpandableDrawer.Modal
        onBack={closeDrawer}
        title={drawerTitle}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEsc={false}
        isOpen={isOpen}
      >
        {renderInside(closeDrawer)}
      </ExpandableDrawer.Modal>
    </>
  );
};
