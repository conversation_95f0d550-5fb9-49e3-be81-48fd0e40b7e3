import { FC, memo, PropsWithChildren, useMemo } from 'react';

import isEmpty from 'lodash/isEmpty';

import { Text, TooltipWrapperButton } from '@/components';
import { useBreakpointsContext } from '@/contexts';
import { TOOLTIP_MAX_LINES } from '@/contracts/tooltip';
import { ObjectiveModel } from '@/performance/objectives/domain';
import { generateLineClampStyle } from '@/utils';

export const SharedWithTooltip: FC<
  PropsWithChildren<{ objective: ObjectiveModel }>
> = memo(({ objective: { cascadedTo, alignedWith, tags, lead }, children }) => {
  const { isMobile } = useBreakpointsContext();

  const filteredCascadedTo = useMemo(
    () =>
      cascadedTo
        ?.filter(({ employeeId }) => employeeId !== lead?.employeeId)
        .map(({ fullNameEnglish }) => fullNameEnglish)
        .join(', '),
    [cascadedTo, lead]
  );

  const isSharedWith = useMemo(
    () => !isEmpty(cascadedTo) && !isEmpty(alignedWith),
    [alignedWith, cascadedTo]
  );

  const drawerTitle = useMemo((): string => {
    if (!isMobile) return '';

    if (isSharedWith) return 'Shared With';

    return `${tags?.includes('Cascaded') ? 'Cascaded' : 'Re-Cascaded'} To`;
  }, [isMobile, isSharedWith, tags]);

  return (
    <TooltipWrapperButton
      title={drawerTitle}
      tooltipText={
        <div className="flex max-w-[250px] flex-col gap-y-8">
          {cascadedTo && !isEmpty(cascadedTo) && (
            <>
              {(isMobile ? isSharedWith : true) && (
                <Text className="text-cta-2-medium md:text-tooltip-text">
                  {tags?.includes('Cascaded') ? 'Cascaded' : 'Re-cascaded'} to:
                </Text>
              )}
              <span className="inline-flex flex-col gap-y-[13px]">
                {filteredCascadedTo && (
                  <Text
                    className="text-body-3-regular md:text-tooltip-text"
                    style={generateLineClampStyle(TOOLTIP_MAX_LINES)}
                  >
                    {filteredCascadedTo}
                  </Text>
                )}
                {lead && (
                  <Text
                    as="p"
                    className="whitespace-pre-wrap text-body-3-regular md:text-tooltip-text"
                  >
                    {'Objective Lead: '}
                    {lead?.fullNameEnglish}
                  </Text>
                )}
              </span>
            </>
          )}
          {!!(alignedWith || []).length && (
            <>
              <Text className="text-cta-2-medium md:text-tooltip-text">
                Aligned with:
              </Text>
              <Text
                className="text-body-3-regular md:text-tooltip-text"
                style={generateLineClampStyle(TOOLTIP_MAX_LINES)}
              >
                {alignedWith
                  ?.map(({ fullNameEnglish }) => fullNameEnglish)
                  .join(', ')}
              </Text>
            </>
          )}
        </div>
      }
    >
      {({ onTouchStart } = {}) => (
        <div
          className="flex items-center justify-self-start"
          onTouchStart={onTouchStart}
        >
          {children}
        </div>
      )}
    </TooltipWrapperButton>
  );
});
