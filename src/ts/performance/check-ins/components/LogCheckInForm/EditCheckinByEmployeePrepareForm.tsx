import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { LogCheckInSchema } from '@performance/check-ins/components/LogCheckInForm/schema';
import { EmployeeCheckInFormModel } from '@performance/check-ins/domain/check-ins/models';
import isEqual from 'lodash/isEqual';

import { EditTalentConnectFormForEmployee } from './EditTalentConnectFormForEmployee';

interface EditCheckinByEmployeePrepareFormProps {
  checkInFormModel: Partial<EmployeeCheckInFormModel>;
  checkInId: string;
}

export interface EditCheckinByEmployeePrepareFormRef {
  isValid: boolean;
  hasUnsavedChanges: boolean;
}

export const EditCheckinByEmployeePrepareForm = forwardRef<
  EditCheckinByEmployeePrepareFormRef,
  EditCheckinByEmployeePrepareFormProps
>(({ checkInFormModel, checkInId }, ref) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const formContext = useForm<EmployeeCheckInFormModel>({
    mode: 'onChange',
    resolver: yupResolver(LogCheckInSchema),
    defaultValues: checkInFormModel
  });

  const {
    formState: { defaultValues, isValid },
    watch
  } = formContext;

  useEffect(() => {
    const { unsubscribe } = watch((values) => {
      setHasUnsavedChanges(!isEqual(defaultValues, values));
    });

    return unsubscribe;
  }, [defaultValues, watch]);

  useImperativeHandle(
    ref,
    () => ({
      isValid,
      hasUnsavedChanges
    }),
    [hasUnsavedChanges, isValid]
  );

  return (
    <FormProvider {...formContext}>
      <EditTalentConnectFormForEmployee
        checkInId={checkInId}
        hasUnsavedChanges={hasUnsavedChanges}
      />
    </FormProvider>
  );
});
