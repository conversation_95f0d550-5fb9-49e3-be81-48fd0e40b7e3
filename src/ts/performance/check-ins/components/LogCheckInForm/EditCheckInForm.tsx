import { FC, useCallback } from 'react';
import { SubmitHandler } from 'react-hook-form';

import { useDeleteCheckInWithConformation } from '@performance/check-ins/domain/check-ins/hooks/useDeleteCheckInWithConformation';

import { ConfirmationModal } from '@/components';
import { useToastContext } from '@/contexts';

import { ManageCheckInForm } from './ManageCheckInForm';

import { useUpdateSubordinateCheckIn } from '../../domain';
import { EmployeeCheckInFormModel } from '../../domain/check-ins/models';

export interface EditCheckInFormProps {
  employeeId: string;
  checkInId: string;
  hasUnsavedChanges: boolean;
  closeModal: (force?: boolean, reopenDetailsDrawer?: boolean) => void;
}

export const EditCheckInForm: FC<EditCheckInFormProps> = ({
  employeeId,
  checkInId,
  hasUnsavedChanges,
  closeModal
}) => {
  const {
    isDeleteConfirmationOpen,
    isDeletePending,
    closeDeleteConfirmation,
    openDeleteConfirmation,
    onDeleteAction,
    text
  } = useDeleteCheckInWithConformation({
    employeeId,
    checkInId,
    closeModal
  });

  const { toast } = useToastContext();

  const { mutate: update, isPending: isUpdatePending } =
    useUpdateSubordinateCheckIn({
      subordinateId: employeeId,
      id: checkInId,
      onSuccess: () => {
        toast.success('Talent Connect log was successfully updated');
        closeModal(true);
      },
      onError: () => {
        toast.error('An error occurred, Talent Connect log was not updated');
      }
    });

  const saveChanges = useCallback<SubmitHandler<EmployeeCheckInFormModel>>(
    (data) => {
      update(data);
    },
    [update]
  );

  return (
    <>
      <ManageCheckInForm
        isEditForm={true}
        hasUnsavedChanges={hasUnsavedChanges}
        closeModal={closeModal}
        isPending={isUpdatePending || isDeletePending}
        saveChanges={saveChanges}
        deleteCheckIn={openDeleteConfirmation}
      />
      <ConfirmationModal
        header={text.header}
        title={text.title}
        description={text.description}
        isOpen={isDeleteConfirmationOpen}
        isLoading={false}
        onClose={closeDeleteConfirmation}
        onAction={onDeleteAction}
      />
    </>
  );
};
