import { FC, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { DataColumnProps, DataRowProps } from '@epam/uui-core';
import { EmployeeCheckInLogTalentConnectButton } from '@performance/check-ins/pages/EmployeeCheckInsPage/components/EmployeeCheckIns/EmployeeCheckInLogTalentConnectButton';
import { CheckInsKebabActionForEmployee } from '@performance/components/CheckIns/CheckInsTable/CheckInTableKebab';
import { CheckInTopics } from '@performance/components/CheckIns/CheckInsTable/CheckInTopics';
import { useCurrentEmployeePermissions } from '@performance/domain';

import { NoItemsAvailable } from '@oh/assets/illustrations';
import { EmptyDataScreen, EmptyDataScreenTypes } from '@oh/components';

import { ButtonVariant } from '@/components';
import {
  CellContent,
  PerformanceDataTable,
  useDataSourceState,
  UserInfo,
  UserInfoSize
} from '@/components';
import { Drawer } from '@/constants';
import { useToggleVisibility } from '@/hooks';
import {
  MyTeamCheckInParams,
  MyTeamCheckInsRowModel,
  MyTeamCheckInsRowType,
  useMyTeamCheckInsDesktop
} from '@/performance/check-ins/domain';
import { VISIBLE_ROW_NUMBER } from '@/performance/constants';

import { LastTalentConnect } from './LastTalentConnect';

const PAGE_SIZE = 7;

const renderNoResults = () => (
  <EmptyDataScreen
    type={EmptyDataScreenTypes.NoResults}
    icon={<NoItemsAvailable />}
    title="You have no reportees"
    subtitle="It seems that there are no reportees"
    className="h-auto"
  />
);

export const TeamCheckInsTable: FC = () => {
  const navigate = useNavigate();
  const { canViewCheckIn } = useCurrentEmployeePermissions();
  const { toggleDrawer } = useToggleVisibility();

  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize: PAGE_SIZE
  });

  const queryParams: MyTeamCheckInParams = {
    ...pagination
  };

  const queryResult = useMyTeamCheckInsDesktop(queryParams);

  const openEmployeeViewDrawer = useCallback(
    (checkInId: string, employeeId: string) => {
      if (canViewCheckIn) {
        toggleDrawer({
          name: Drawer.TalentConnectDetails,
          employeeId,
          checkInId
        });
      }
    },
    [toggleDrawer, canViewCheckIn]
  );

  const openCheckInHistory = useCallback(
    (anyId: string, props?: DataRowProps<MyTeamCheckInsRowModel, string>) => {
      if (props?.value?.type === MyTeamCheckInsRowType.SeeAll) {
        navigate(`/employees/${props.parentId}/check-ins`);
        return;
      }
      if (!props?.parentId) {
        navigate(`/employees/${anyId}/check-ins`);
      } else {
        openEmployeeViewDrawer(props.id, props.parentId);
      }
    },
    [navigate, openEmployeeViewDrawer]
  );

  const columns = useColumns({ openEmployeeViewDrawer });

  return (
    <PerformanceDataTable
      {...dataSource}
      dataAttributes="tcDirectReporteesTable"
      showContainer
      queryResult={queryResult}
      columns={columns}
      onRowClick={openCheckInHistory}
      renderNoResults={renderNoResults}
      isFoldedByDefault={() => true}
      paginationContainerClassName="!mb-20"
      maxVisibleRowsWithoutScroll={VISIBLE_ROW_NUMBER}
    />
  );
};

interface UseColumnsProps {
  openEmployeeViewDrawer: (checkInId: string, employeeId: string) => void;
}

function useColumns({ openEmployeeViewDrawer }: UseColumnsProps) {
  const columns: DataColumnProps<MyTeamCheckInsRowModel>[] = useMemo(
    () => [
      {
        key: 'name',
        caption: 'Employee Name',
        render: (item) => (
          <CellContent classNames="line-clamp-2">
            <NameContent rowItem={item} />
          </CellContent>
        ),
        width: 440,
        grow: 1
      },
      {
        key: 'meetingDate',
        caption: 'Last Talent Connect',
        render: (params) => (
          <CellContent>
            {params.type === MyTeamCheckInsRowType.SeeAll && (
              <div className="ml-[35px] text-cta-1-medium text-link-rested">
                See All
              </div>
            )}
            {params.type === MyTeamCheckInsRowType.Employee && (
              <LastTalentConnect
                meetingDate={params.meetingDate}
                checkInOverdue={params.checkInOverdue}
              />
            )}
            {params.type === MyTeamCheckInsRowType.CheckIn && (
              <LastTalentConnect
                meetingDate={params.meetingDate}
                checkInOverdue={false}
              />
            )}
          </CellContent>
        ),
        width: 240,
        grow: 1
      },
      {
        key: 'totalCheckIns',
        caption: 'Total Talent Connects',
        render: (item) => <CellContent>{item.totalCheckIns || ''}</CellContent>,
        width: 160,
        grow: 1
      },
      {
        key: 'actions',
        render: (item) => {
          if (item.type === MyTeamCheckInsRowType.SeeAll) {
            return null;
          }
          return item.type === MyTeamCheckInsRowType.Employee ? (
            <EmployeeCheckInLogTalentConnectButton
              employeeId={item.id}
              variant={ButtonVariant.Secondary}
            />
          ) : (
            <div>
              {item.parentId && (
                <CheckInsKebabActionForEmployee
                  item={{ ...item, parentId: item.parentId }}
                  onViewClick={(checkInId) =>
                    item.parentId &&
                    openEmployeeViewDrawer(checkInId, item.parentId)
                  }
                />
              )}
            </div>
          );
        },
        width: 220,
        fix: 'right',
        justifyContent: 'end',
        allowResizing: false
      }
    ],
    [openEmployeeViewDrawer]
  );

  return columns;
}

interface NameContentProps {
  rowItem: MyTeamCheckInsRowModel;
}

const NameContent: FC<NameContentProps> = ({ rowItem }) => {
  switch (rowItem.type) {
    case MyTeamCheckInsRowType.CheckIn:
      return <CheckInTopics topics={rowItem.topics} title={rowItem.name} />;
    case MyTeamCheckInsRowType.Employee:
      return (
        <UserInfo
          size={UserInfoSize.Medium}
          fullName={rowItem.name}
          email={rowItem.email}
          companyName={rowItem.companyName}
          positionName={rowItem.positionName}
          isSubtitleTruncated
        />
      );
  }
};
