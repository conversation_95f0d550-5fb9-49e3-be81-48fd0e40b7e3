import { FC, useCallback } from 'react';

import { useDataSourceState } from '@/components';
import { Drawer } from '@/constants';
import { useToggleVisibility } from '@/hooks';
import { useEmployeeCheckIns } from '@/performance/check-ins/domain/check-ins/hooks/useEmployeeCheckIns';
import { GetEmployeeCheckInsParams } from '@/performance/check-ins/domain/check-ins/models';
import { CheckInsTable } from '@/performance/components';
import { SUBORDINATE_TALENT_CONNECT_TABLE_SUBTITLE } from '@/performance/constants';
import { useCurrentEmployeePermissions } from '@/performance/domain';

const PAGE_SIZE = 500;

export interface EmployeeCheckInsTableProps {
  employeeId: string;
  readonly?: boolean;
}

export const EmployeeCheckInsTable: FC<EmployeeCheckInsTableProps> = ({
  employeeId,
  readonly = false
}) => {
  const { toggleDrawer } = useToggleVisibility();
  const { canViewCheckIn } = useCurrentEmployeePermissions();

  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize: PAGE_SIZE
  });

  const queryParams: GetEmployeeCheckInsParams = {
    employeeId,
    ...pagination
  };

  const queryResult = useEmployeeCheckIns(queryParams);

  const handleRowClick = useCallback<(checkInId: string) => void>(
    (checkInId) => {
      if (canViewCheckIn && !readonly) {
        toggleDrawer({
          name: Drawer.TalentConnectDetails,
          employeeId,
          checkInId
        });
      }
    },
    [employeeId, canViewCheckIn, toggleDrawer, readonly]
  );

  return (
    <CheckInsTable
      dataSource={dataSource}
      dataAttributes="tcTableEmployee"
      onRowClick={handleRowClick}
      queryResult={queryResult}
      emptyScreenSubtitle={SUBORDINATE_TALENT_CONNECT_TABLE_SUBTITLE}
    />
  );
};
