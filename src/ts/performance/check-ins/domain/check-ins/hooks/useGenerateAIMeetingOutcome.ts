import { useMutation } from '@tanstack/react-query';

import { usePerformanceServiceApi } from '@/performance/domain';
import { formatHTMLFromAIMarkupWithHeaders } from '@/utils';

import { GenerateAIMeetingOutcomeRequestModel } from '../models';
import { transformAISummaryRequest } from '../transformers';

export function useGenerateAIMeetingOutcome() {
  const { checkInsApi } = usePerformanceServiceApi();

  return useMutation({
    mutationFn: async (request: GenerateAIMeetingOutcomeRequestModel) => {
      const response = await checkInsApi.v1CheckInsSummaryAiGeneratePost({
        aiCheckinSummaryRequest: transformAISummaryRequest(request)
      });

      const aiSummary = response.aiSummary || '';

      return formatHTMLFromAIMarkupWithHeaders(aiSummary);
    }
  });
}
