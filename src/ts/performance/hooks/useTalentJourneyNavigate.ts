import { useCallback } from 'react';

import { MessageType, ProfileRedirectPayload } from '@oh/contracts';

import { useAppContext } from '@/contexts';

export const useTalentJourneyNavigate = (email?: string) => {
  const { messageToHostFunction } = useAppContext();
  return useCallback(() => {
    if (email) {
      messageToHostFunction?.({
        type: MessageType.ProfileRedirect,
        payload: {
          externalId: `${email}/talent-journey`
        } as ProfileRedirectPayload
      });
    }
  }, [messageToHostFunction, email]);
};
