/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ObjectiveSkillDto
 */
export interface ObjectiveSkillDto {
    /**
     * 
     * @type {string}
     * @memberof ObjectiveSkillDto
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof ObjectiveSkillDto
     */
    name?: string | null;
}

/**
 * Check if a given object implements the ObjectiveSkillDto interface.
 */
export function instanceOfObjectiveSkillDto(value: object): value is ObjectiveSkillDto {
    return true;
}

export function ObjectiveSkillDtoFromJSON(json: any): ObjectiveSkillDto {
    return ObjectiveSkillDtoFromJSONTyped(json, false);
}

export function ObjectiveSkillDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): ObjectiveSkillDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
    };
}

export function ObjectiveSkillDtoToJSON(value?: ObjectiveSkillDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

