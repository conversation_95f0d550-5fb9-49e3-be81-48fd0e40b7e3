/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AtrTemplateFieldUpdateDto } from './AtrTemplateFieldUpdateDto';
import {
    AtrTemplateFieldUpdateDtoFromJSON,
    AtrTemplateFieldUpdateDtoFromJSONTyped,
    AtrTemplateFieldUpdateDtoToJSON,
} from './AtrTemplateFieldUpdateDto';

/**
 * 
 * @export
 * @interface UpsertAtrTemplateRequest
 */
export interface UpsertAtrTemplateRequest {
    /**
     * 
     * @type {string}
     * @memberof UpsertAtrTemplateRequest
     */
    Name: string | null;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    MinObjectiveWeight: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    MaxObjectiveWeight: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    MinObjectiveAmount: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    MaxObjectiveAmount: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    DaysSinceObjectiveCreation: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    DaysSinceLastCheckIn?: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    MaxObjectiveSkills?: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    MinTotalWeightForApproval?: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    NumberMilestonesGeneratedByAi?: number;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    NumberTasksGeneratedByAi?: number;
    /**
     * 
     * @type {boolean}
     * @memberof UpsertAtrTemplateRequest
     */
    AllowEmployeeDeleteApprovedObjective?: boolean;
    /**
     * 
     * @type {number}
     * @memberof UpsertAtrTemplateRequest
     */
    DaysCheckInEditable: number;
    /**
     * 
     * @type {boolean}
     * @memberof UpsertAtrTemplateRequest
     */
    MandatoryObjectivesApproval: boolean;
    /**
     * 
     * @type {Array<AtrTemplateFieldUpdateDto>}
     * @memberof UpsertAtrTemplateRequest
     */
    Fields: Array<AtrTemplateFieldUpdateDto> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof UpsertAtrTemplateRequest
     */
    UserGroups: Array<number> | null;
}

/**
 * Check if a given object implements the UpsertAtrTemplateRequest interface.
 */
export function instanceOfUpsertAtrTemplateRequest(value: object): value is UpsertAtrTemplateRequest {
    if (!('Name' in value) || value['Name'] === undefined) return false;
    if (!('MinObjectiveWeight' in value) || value['MinObjectiveWeight'] === undefined) return false;
    if (!('MaxObjectiveWeight' in value) || value['MaxObjectiveWeight'] === undefined) return false;
    if (!('MinObjectiveAmount' in value) || value['MinObjectiveAmount'] === undefined) return false;
    if (!('MaxObjectiveAmount' in value) || value['MaxObjectiveAmount'] === undefined) return false;
    if (!('DaysSinceObjectiveCreation' in value) || value['DaysSinceObjectiveCreation'] === undefined) return false;
    if (!('DaysCheckInEditable' in value) || value['DaysCheckInEditable'] === undefined) return false;
    if (!('MandatoryObjectivesApproval' in value) || value['MandatoryObjectivesApproval'] === undefined) return false;
    if (!('Fields' in value) || value['Fields'] === undefined) return false;
    if (!('UserGroups' in value) || value['UserGroups'] === undefined) return false;
    return true;
}

export function UpsertAtrTemplateRequestFromJSON(json: any): UpsertAtrTemplateRequest {
    return UpsertAtrTemplateRequestFromJSONTyped(json, false);
}

export function UpsertAtrTemplateRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): UpsertAtrTemplateRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'Name': json['Name'],
        'MinObjectiveWeight': json['MinObjectiveWeight'],
        'MaxObjectiveWeight': json['MaxObjectiveWeight'],
        'MinObjectiveAmount': json['MinObjectiveAmount'],
        'MaxObjectiveAmount': json['MaxObjectiveAmount'],
        'DaysSinceObjectiveCreation': json['DaysSinceObjectiveCreation'],
        'DaysSinceLastCheckIn': json['DaysSinceLastCheckIn'] == null ? undefined : json['DaysSinceLastCheckIn'],
        'MaxObjectiveSkills': json['MaxObjectiveSkills'] == null ? undefined : json['MaxObjectiveSkills'],
        'MinTotalWeightForApproval': json['MinTotalWeightForApproval'] == null ? undefined : json['MinTotalWeightForApproval'],
        'NumberMilestonesGeneratedByAi': json['NumberMilestonesGeneratedByAi'] == null ? undefined : json['NumberMilestonesGeneratedByAi'],
        'NumberTasksGeneratedByAi': json['NumberTasksGeneratedByAi'] == null ? undefined : json['NumberTasksGeneratedByAi'],
        'AllowEmployeeDeleteApprovedObjective': json['AllowEmployeeDeleteApprovedObjective'] == null ? undefined : json['AllowEmployeeDeleteApprovedObjective'],
        'DaysCheckInEditable': json['DaysCheckInEditable'],
        'MandatoryObjectivesApproval': json['MandatoryObjectivesApproval'],
        'Fields': (json['Fields'] == null ? null : (json['Fields'] as Array<any>).map(AtrTemplateFieldUpdateDtoFromJSON)),
        'UserGroups': json['UserGroups'],
    };
}

export function UpsertAtrTemplateRequestToJSON(value?: UpsertAtrTemplateRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'Name': value['Name'],
        'MinObjectiveWeight': value['MinObjectiveWeight'],
        'MaxObjectiveWeight': value['MaxObjectiveWeight'],
        'MinObjectiveAmount': value['MinObjectiveAmount'],
        'MaxObjectiveAmount': value['MaxObjectiveAmount'],
        'DaysSinceObjectiveCreation': value['DaysSinceObjectiveCreation'],
        'DaysSinceLastCheckIn': value['DaysSinceLastCheckIn'],
        'MaxObjectiveSkills': value['MaxObjectiveSkills'],
        'MinTotalWeightForApproval': value['MinTotalWeightForApproval'],
        'NumberMilestonesGeneratedByAi': value['NumberMilestonesGeneratedByAi'],
        'NumberTasksGeneratedByAi': value['NumberTasksGeneratedByAi'],
        'AllowEmployeeDeleteApprovedObjective': value['AllowEmployeeDeleteApprovedObjective'],
        'DaysCheckInEditable': value['DaysCheckInEditable'],
        'MandatoryObjectivesApproval': value['MandatoryObjectivesApproval'],
        'Fields': (value['Fields'] == null ? null : (value['Fields'] as Array<any>).map(AtrTemplateFieldUpdateDtoToJSON)),
        'UserGroups': value['UserGroups'],
    };
}

