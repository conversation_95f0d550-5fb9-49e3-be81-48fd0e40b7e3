/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ObjectiveTargetTypeDto
 */
export interface ObjectiveTargetTypeDto {
    /**
     * 
     * @type {string}
     * @memberof ObjectiveTargetTypeDto
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof ObjectiveTargetTypeDto
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ObjectiveTargetTypeDto
     */
    displayName?: string | null;
}

/**
 * Check if a given object implements the ObjectiveTargetTypeDto interface.
 */
export function instanceOfObjectiveTargetTypeDto(value: object): value is ObjectiveTargetTypeDto {
    return true;
}

export function ObjectiveTargetTypeDtoFromJSON(json: any): ObjectiveTargetTypeDto {
    return ObjectiveTargetTypeDtoFromJSONTyped(json, false);
}

export function ObjectiveTargetTypeDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): ObjectiveTargetTypeDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'displayName': json['displayName'] == null ? undefined : json['displayName'],
    };
}

export function ObjectiveTargetTypeDtoToJSON(value?: ObjectiveTargetTypeDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
        'displayName': value['displayName'],
    };
}

