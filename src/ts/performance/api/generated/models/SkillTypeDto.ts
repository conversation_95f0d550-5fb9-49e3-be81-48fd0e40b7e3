/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SkillTypeDto
 */
export interface SkillTypeDto {
    /**
     * 
     * @type {string}
     * @memberof SkillTypeDto
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof SkillTypeDto
     */
    name?: string | null;
}

/**
 * Check if a given object implements the SkillTypeDto interface.
 */
export function instanceOfSkillTypeDto(value: object): value is SkillTypeDto {
    return true;
}

export function SkillTypeDtoFromJSON(json: any): SkillTypeDto {
    return SkillTypeDtoFromJSONTyped(json, false);
}

export function SkillTypeDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): SkillTypeDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
    };
}

export function SkillTypeDtoToJSON(value?: SkillTypeDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

