/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface WorkflowHistoryStepResponse
 */
export interface WorkflowHistoryStepResponse {
    /**
     * 
     * @type {number}
     * @memberof WorkflowHistoryStepResponse
     */
    id?: number;
    /**
     * 
     * @type {string}
     * @memberof WorkflowHistoryStepResponse
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof WorkflowHistoryStepResponse
     */
    description?: string | null;
}

/**
 * Check if a given object implements the WorkflowHistoryStepResponse interface.
 */
export function instanceOfWorkflowHistoryStepResponse(value: object): value is WorkflowHistoryStepResponse {
    return true;
}

export function WorkflowHistoryStepResponseFromJSON(json: any): WorkflowHistoryStepResponse {
    return WorkflowHistoryStepResponseFromJSONTyped(json, false);
}

export function WorkflowHistoryStepResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): WorkflowHistoryStepResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'description': json['description'] == null ? undefined : json['description'],
    };
}

export function WorkflowHistoryStepResponseToJSON(value?: WorkflowHistoryStepResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
        'description': value['description'],
    };
}

