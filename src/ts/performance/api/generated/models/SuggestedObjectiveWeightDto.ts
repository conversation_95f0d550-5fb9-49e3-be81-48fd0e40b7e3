/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SuggestedObjectiveWeightDto
 */
export interface SuggestedObjectiveWeightDto {
    /**
     * 
     * @type {string}
     * @memberof SuggestedObjectiveWeightDto
     */
    id?: string;
    /**
     * 
     * @type {number}
     * @memberof SuggestedObjectiveWeightDto
     */
    objectiveWeightPercentage?: number;
}

/**
 * Check if a given object implements the SuggestedObjectiveWeightDto interface.
 */
export function instanceOfSuggestedObjectiveWeightDto(value: object): value is SuggestedObjectiveWeightDto {
    return true;
}

export function SuggestedObjectiveWeightDtoFromJSON(json: any): SuggestedObjectiveWeightDto {
    return SuggestedObjectiveWeightDtoFromJSONTyped(json, false);
}

export function SuggestedObjectiveWeightDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): SuggestedObjectiveWeightDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'objectiveWeightPercentage': json['objectiveWeightPercentage'] == null ? undefined : json['objectiveWeightPercentage'],
    };
}

export function SuggestedObjectiveWeightDtoToJSON(value?: SuggestedObjectiveWeightDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'objectiveWeightPercentage': value['objectiveWeightPercentage'],
    };
}

