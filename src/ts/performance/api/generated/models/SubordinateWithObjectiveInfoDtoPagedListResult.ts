/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SubordinateWithObjectiveInfoDto } from './SubordinateWithObjectiveInfoDto';
import {
    SubordinateWithObjectiveInfoDtoFromJSON,
    SubordinateWithObjectiveInfoDtoFromJSONTyped,
    SubordinateWithObjectiveInfoDtoToJSON,
} from './SubordinateWithObjectiveInfoDto';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
} from './PaginationData';

/**
 * 
 * @export
 * @interface SubordinateWithObjectiveInfoDtoPagedListResult
 */
export interface SubordinateWithObjectiveInfoDtoPagedListResult {
    /**
     * 
     * @type {Array<SubordinateWithObjectiveInfoDto>}
     * @memberof SubordinateWithObjectiveInfoDtoPagedListResult
     */
    items?: Array<SubordinateWithObjectiveInfoDto> | null;
    /**
     * 
     * @type {PaginationData}
     * @memberof SubordinateWithObjectiveInfoDtoPagedListResult
     */
    paging?: PaginationData;
}

/**
 * Check if a given object implements the SubordinateWithObjectiveInfoDtoPagedListResult interface.
 */
export function instanceOfSubordinateWithObjectiveInfoDtoPagedListResult(value: object): value is SubordinateWithObjectiveInfoDtoPagedListResult {
    return true;
}

export function SubordinateWithObjectiveInfoDtoPagedListResultFromJSON(json: any): SubordinateWithObjectiveInfoDtoPagedListResult {
    return SubordinateWithObjectiveInfoDtoPagedListResultFromJSONTyped(json, false);
}

export function SubordinateWithObjectiveInfoDtoPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): SubordinateWithObjectiveInfoDtoPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': json['items'] == null ? undefined : ((json['items'] as Array<any>).map(SubordinateWithObjectiveInfoDtoFromJSON)),
        'paging': json['paging'] == null ? undefined : PaginationDataFromJSON(json['paging']),
    };
}

export function SubordinateWithObjectiveInfoDtoPagedListResultToJSON(value?: SubordinateWithObjectiveInfoDtoPagedListResult | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'items': value['items'] == null ? undefined : ((value['items'] as Array<any>).map(SubordinateWithObjectiveInfoDtoToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

