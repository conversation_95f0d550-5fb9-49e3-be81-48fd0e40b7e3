/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.PerformanceService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 3.663
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ObjectiveTaskDto
 */
export interface ObjectiveTaskDto {
    /**
     * 
     * @type {string}
     * @memberof ObjectiveTaskDto
     */
    description?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof ObjectiveTaskDto
     */
    isCompleted?: boolean;
    /**
     * 
     * @type {Date}
     * @memberof ObjectiveTaskDto
     */
    dueDate?: Date | null;
}

/**
 * Check if a given object implements the ObjectiveTaskDto interface.
 */
export function instanceOfObjectiveTaskDto(value: object): value is ObjectiveTaskDto {
    return true;
}

export function ObjectiveTaskDtoFromJSON(json: any): ObjectiveTaskDto {
    return ObjectiveTaskDtoFromJSONTyped(json, false);
}

export function ObjectiveTaskDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): ObjectiveTaskDto {
    if (json == null) {
        return json;
    }
    return {
        
        'description': json['description'] == null ? undefined : json['description'],
        'isCompleted': json['isCompleted'] == null ? undefined : json['isCompleted'],
        'dueDate': json['dueDate'] == null ? undefined : (new Date(json['dueDate'])),
    };
}

export function ObjectiveTaskDtoToJSON(value?: ObjectiveTaskDto | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'description': value['description'],
        'isCompleted': value['isCompleted'],
        'dueDate': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString().substring(0,10)),
    };
}

