import { useQuery } from '@tanstack/react-query';

export interface CreateUseDataBaseProps<TServiceApi, TParams, TData> {
  useServiceApi: () => TServiceApi;
  getKey: (params: TParams) => readonly unknown[];
  getData: (api: TServiceApi, params: TParams) => Promise<TData>;
}

export function createUseDataBase<TServiceApi, TParams, TData>({
  useServiceApi,
  getKey,
  getData
}: CreateUseDataBaseProps<TServiceApi, TParams, TData>) {
  return function useData(params: TParams, enabled = true) {
    const api = useServiceApi();

    return useQuery({
      queryKey: getKey(params),
      queryFn: () => getData(api, params),
      enabled
    });
  };
}
