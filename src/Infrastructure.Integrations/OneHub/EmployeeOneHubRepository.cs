using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Employees.Queries.Models;
using OneTalent.FeedbackService.Application.Employees.Queries.Repositories;
using OneTalent.FeedbackService.Infrastructure.Integrations.OneHub.Models;
using System.Runtime.CompilerServices;

namespace OneTalent.FeedbackService.Infrastructure.Integrations.OneHub;

internal class EmployeeOneHubRepository(IOneHubApiClient oneHubApiClient) : IEmployeeSearchApiRepository
{
    public async IAsyncEnumerable<EmployeeSearchModel> SearchEmployeesAsync(
        string searchString,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        const int take = 100;
        var skip = 0;
        int returnedBatchLength;

        do
        {
            var batch = await oneHubApiClient.SearchEmployeesAsync(
                new SearchEmployeesRequest(searchString, skip, take),
                cancellationToken);

            returnedBatchLength = batch.Length;

            foreach (var employee in batch)
            {
                yield return new(
                    new ItemId(employee.EmployeeNumber),
                    employee.DisplayName);
            }

            skip += returnedBatchLength;

        } while (returnedBatchLength == take);
    }
}
