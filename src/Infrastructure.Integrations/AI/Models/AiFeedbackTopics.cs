using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace OneTalent.FeedbackService.Infrastructure.Integrations.AI.Models;

public enum AiFeedbackTopics
{
    [JsonStringEnumMemberName("objective")]
    Objective = 2,

    [JsonStringEnumMemberName("task_or_milestone")]
    TaskOrMilestone = 3,

    [JsonStringEnumMemberName("career_aspiration")]
    Aspiration = 4,

    [JsonStringEnumMemberName("general")]
    General = 5,

    [JsonStringEnumMemberName("regular_1_to_1")]
    RegularOneOnOneMeetin = 6,

    [JsonStringEnumMemberName("periodic_check_in")]
    PeriodicCheckIn = 7,

    [JsonStringEnumMemberName("skill_validation")]
    SkillValidations = 8,

    [JsonStringEnumMemberName("learning_plan")]
    LearningPlan = 9,

    [JsonStringEnumMemberName("training_plan_approval")]
    TrainingPlanApproval = 10,

    [JsonStringEnumMemberName("set_objectives")]
    SetObjectives = 11,
}
