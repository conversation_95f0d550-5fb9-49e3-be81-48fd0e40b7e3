import { IllustrationMessage, IllustrationVariant } from '@ot/onetalent-ui-kit';

export const IllustrationMessageError = () => (
  <div className="flex h-[468px] w-full items-center justify-center">
    <IllustrationMessage
      illustrationVariant={IllustrationVariant.Error}
      title="Sorry, an error has occurred"
      description={"We can't seem to find the page you are looking for"}
    />
  </div>
);
