<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="path-1-inside-1_27097_102428" fill="white">
<path d="M101 59C103.209 59 105.019 57.2055 104.818 55.0055C104.416 50.5967 103.351 46.2661 101.651 42.1619C99.4395 36.8236 96.1985 31.9731 92.1127 27.8873C88.0269 23.8015 83.1764 20.5605 77.8381 18.3493C73.7339 16.6493 69.4033 15.5836 64.9945 15.1817C62.7945 14.9811 61 16.7909 61 19V25.2778C61 27.4869 62.7988 29.2501 64.988 29.5465C67.5201 29.8894 70.0031 30.5581 72.3742 31.5402C75.9803 33.0339 79.2568 35.2232 82.0168 37.9832C84.7767 40.7432 86.9661 44.0197 88.4598 47.6258C89.4419 49.9969 90.1106 52.4799 90.4535 55.012C90.7499 57.2012 92.5131 59 94.7222 59H101Z"/>
</mask>
<g id="spinner" style="transform-origin: 60px 60px; animation: spin 1.5s linear infinite;">
<path d="M101 59C103.209 59 105.019 57.2055 104.818 55.0055C104.416 50.5967 103.351 46.2661 101.651 42.1619C99.4395 36.8236 96.1985 31.9731 92.1127 27.8873C88.0269 23.8015 83.1764 20.5605 77.8381 18.3493C73.7339 16.6493 69.4033 15.5836 64.9945 15.1817C62.7945 14.9811 61 16.7909 61 19V25.2778C61 27.4869 62.7988 29.2501 64.988 29.5465C67.5201 29.8894 70.0031 30.5581 72.3742 31.5402C75.9803 33.0339 79.2568 35.2232 82.0168 37.9832C84.7767 40.7432 86.9661 44.0197 88.4598 47.6258C89.4419 49.9969 90.1106 52.4799 90.4535 55.012C90.7499 57.2012 92.5131 59 94.7222 59H101Z" fill="url(#paint0_radial_27097_102428)" stroke="url(#paint1_linear_27097_102428)" stroke-opacity="0.7" stroke-width="2" mask="url(#path-1-inside-1_27097_102428)"/>
</g>
<foreignObject x="-4" y="-4" width="128" height="128"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(12px);clip-path:url(#bgblur_0_27097_102428_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_d_27097_102428)" data-figma-bg-blur-radius="24">
<mask id="path-2-inside-2_27097_102428" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M60 100C82.0914 100 100 82.0914 100 60C100 37.9086 82.0914 20 60 20C37.9086 20 20 37.9086 20 60C20 82.0914 37.9086 100 60 100ZM60 86.6667C74.7276 86.6667 86.6667 74.7276 86.6667 60C86.6667 45.2724 74.7276 33.3333 60 33.3333C45.2724 33.3333 33.3333 45.2724 33.3333 60C33.3333 74.7276 45.2724 86.6667 60 86.6667Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M60 100C82.0914 100 100 82.0914 100 60C100 37.9086 82.0914 20 60 20C37.9086 20 20 37.9086 20 60C20 82.0914 37.9086 100 60 100ZM60 86.6667C74.7276 86.6667 86.6667 74.7276 86.6667 60C86.6667 45.2724 74.7276 33.3333 60 33.3333C45.2724 33.3333 33.3333 45.2724 33.3333 60C33.3333 74.7276 45.2724 86.6667 60 86.6667Z" fill="#A5D1FF" fill-opacity="0.3" shape-rendering="crispEdges"/>
<path d="M100 60H99C99 81.5391 81.5391 99 60 99V100V101C82.6437 101 101 82.6437 101 60H100ZM60 20V21C81.5391 21 99 38.4609 99 60H100H101C101 37.3563 82.6437 19 60 19V20ZM20 60H21C21 38.4609 38.4609 21 60 21V20V19C37.3563 19 19 37.3563 19 60H20ZM60 100V99C38.4609 99 21 81.5391 21 60H20H19C19 82.6437 37.3563 101 60 101V100ZM86.6667 60H85.6667C85.6667 74.1753 74.1753 85.6667 60 85.6667V86.6667V87.6667C75.2799 87.6667 87.6667 75.2799 87.6667 60H86.6667ZM60 33.3333V34.3333C74.1753 34.3333 85.6667 45.8247 85.6667 60H86.6667H87.6667C87.6667 44.7201 75.2799 32.3333 60 32.3333V33.3333ZM33.3333 60H34.3333C34.3333 45.8247 45.8247 34.3333 60 34.3333V33.3333V32.3333C44.7201 32.3333 32.3333 44.7201 32.3333 60H33.3333ZM60 86.6667V85.6667C45.8247 85.6667 34.3333 74.1753 34.3333 60H33.3333H32.3333C32.3333 75.2799 44.7201 87.6667 60 87.6667V86.6667Z" fill="url(#paint2_linear_27097_102428)" fill-opacity="0.2" mask="url(#path-2-inside-2_27097_102428)"/>
</g>
<defs>
<filter id="filter0_d_27097_102428" x="-4" y="-4" width="128" height="128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.354167 0 0 0 0 0.708333 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_27097_102428"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_27097_102428" result="shape"/>
</filter>
<clipPath id="bgblur_0_27097_102428_clip_path" transform="translate(4 4)"><path fill-rule="evenodd" clip-rule="evenodd" d="M60 100C82.0914 100 100 82.0914 100 60C100 37.9086 82.0914 20 60 20C37.9086 20 20 37.9086 20 60C20 82.0914 37.9086 100 60 100ZM60 86.6667C74.7276 86.6667 86.6667 74.7276 86.6667 60C86.6667 45.2724 74.7276 33.3333 60 33.3333C45.2724 33.3333 33.3333 45.2724 33.3333 60C33.3333 74.7276 45.2724 86.6667 60 86.6667Z"/>
</clipPath><radialGradient id="paint0_radial_27097_102428" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(76.8889 44.3333) rotate(128.131) scale(52.2943 47.7301)">
<stop stop-color="#1186FA"/>
<stop offset="1" stop-color="#205CDF"/>
</radialGradient>
<linearGradient id="paint1_linear_27097_102428" x1="61" y1="59" x2="95.6571" y2="33.5945" gradientUnits="userSpaceOnUse">
<stop stop-color="#205CDF"/>
<stop offset="1" stop-color="#1186FA"/>
</linearGradient>
<linearGradient id="paint2_linear_27097_102428" x1="51.6908" y1="63.2168" x2="74.3611" y2="42.6887" gradientUnits="userSpaceOnUse">
<stop stop-color="#A6BEF2"/>
<stop offset="1" stop-color="#ECF5FF"/>
</linearGradient>
</defs>
</svg>
