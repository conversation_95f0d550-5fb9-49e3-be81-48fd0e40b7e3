import { FC } from 'react';

import { ToastProvider } from '@ot/onetalent-ui-kit';

import type { FederationApp } from '@oh/contracts';

import { AuthProvider } from './shared/modules/auth';
import {
  AppConfigProvider,
  AppInsightsProvider,
  AppProvider,
  AppQueryClientProvider,
  AppRouterProvider,
  BreakpointsProvider
} from './shared/providers';
import { OneHubFederatedApplicationProps } from './shared/types';

export type FederationAppProps = FederationApp;

export const withApplication =
  (
    WrappedComponent: FC<OneHubFederatedApplicationProps>
  ): FC<FederationAppProps> =>
  (props) => (
    // OH specific providers
    <AppConfigProvider>
      <AppInsightsProvider>
        <AppQueryClientProvider>
          <AppProvider {...props}>
            <AuthProvider>
              <ToastProvider>
                <AppRouterProvider>
                  {/*Module specific providers*/}
                  <BreakpointsProvider>
                    <WrappedComponent {...props} />
                  </BreakpointsProvider>
                </AppRouterProvider>
              </ToastProvider>
            </AuthProvider>
          </AppProvider>
        </AppQueryClientProvider>
      </AppInsightsProvider>
    </AppConfigProvider>
  );
