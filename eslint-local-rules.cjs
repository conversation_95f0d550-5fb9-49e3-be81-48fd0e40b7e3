module.exports = {
  'require-data-attribute': {
    meta: {
      type: 'problem',
      docs: {
        description:
          'Require a data-attributes attribute on first-level JSX elements for WalkMe and QA automation integration',
        category: 'Best Practices'
      },
      fixable: null,
      schema: []
    },

    create(context) {
      return {
        ReturnStatement(node) {
          const returned = node.argument;

          // Handle JSX return
          if (
            returned?.type === 'JSXElement' ||
            returned?.type === 'JSXFragment'
          ) {
            const children =
              returned.type === 'JSXElement' ? [returned] : returned.children;

            // Only check the first child (root element)
            const firstChild = children[0];
            if (firstChild?.type === 'JSXElement') {
              const openingEl = firstChild.openingElement;
              const tagNameNode = openingEl.name;

              const tagName =
                tagNameNode.type === 'JSXIdentifier' ? tagNameNode.name : null;

              // Validate DOM tag or component alias like 'Comp'
              const isLikelyDOM =
                /^[a-z]/.test(tagName || '') || tagName === 'Comp';

              if (isLikelyDOM) {
                const hasDataAttribute = openingEl.attributes.some(
                  (attr) =>
                    attr.type === 'JSXAttribute' &&
                    attr.name &&
                    /^data-attributes/.test(attr.name.name)
                );

                if (!hasDataAttribute) {
                  context.report({
                    node: openingEl,
                    message:
                      'First-level JSX element is missing a required `data-attributes` attribute for WalkMe and QA automation integration.'
                  });
                }
              }
            }
          }
        }
      };
    }
  }
};
