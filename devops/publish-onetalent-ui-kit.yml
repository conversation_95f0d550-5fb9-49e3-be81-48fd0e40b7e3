trigger:
  branches:
    include:
      - release

pr:
  branches:
    exclude:
      - '*'

pool:
  vmImage: ubuntu-latest

jobs:
  - job: BuildAndPublish
    displayName: 'Build and Publish UI Kit'
    steps:
    - task: npmAuthenticate@0
      inputs:
        feedUrl: 'https://pkgs.dev.azure.com/DevOpsAD/OneTalent/_packaging/onetalent-ui-kit/npm/registry/'
        workingFile: '.npmrc'

    - task: NodeTool@0
      inputs:
        versionSpec: '20.x'
      displayName: 'Install Node.js'

    - script: |
        sudo apt-get update
        npm install -g yarn
      displayName: 'Setup prerequisites'

    - script: 'yarn install'
      displayName: 'yarn install'

    - script: 'yarn build'
      displayName: 'yarn build'

    # Debug before changeset
    - script: |
        echo "Listing .changeset directory before running changeset:"
        ls -la .changeset || echo ".changeset directory not found"
        echo "Running yarn changeset version with verbose output:"
        yarn changeset version --verbose
        echo "Listing files after running changeset:"
        ls -la
      displayName: 'Apply Changeset version & update CHANGELOG'
      # condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/release'))
      condition: false  # disabled for debugging

    - script: |
        echo "Checking for .changeset files:"
        ls -la .changeset || echo ".changeset directory not found"
        echo "Checking for git lock:"
        ls -la .git/index.lock || echo "No git lock"
        echo "Git status:"
        git status
      displayName: 'Debug after Changeset Version'
      condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/release'))

    - script: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "Azure CI"
        git remote set-url origin https://$(System.AccessToken)@dev.azure.com/DevOpsAD/OneTalent/_git/onetalent-ui-kit
        git add .
        git commit -m "chore: version bump & changelog update [skip ci]" || echo "Nothing to commit"
      displayName: 'Commit version changes'
      env:
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/release'))

    - script: |
        echo "Build.SourceBranch: $(Build.SourceBranch)"
        echo "Build.SourceBranchName: $(Build.SourceBranchName)"
      displayName: 'Debug branch variables'
      condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/release'))

    - script: |
        BRANCH_NAME=$(echo $(Build.SourceBranch) | sed 's#refs/heads/##')
        echo "Branch name: $BRANCH_NAME"
        git fetch origin
        if git ls-remote --heads origin $BRANCH_NAME | grep $BRANCH_NAME; then
          git push origin HEAD:refs/heads/$BRANCH_NAME
        else
          echo "Remote branch does not exist. Skipping push."
        fi
      displayName: 'Conditional Push to Branch'
      env:
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/release'))

    - task: Bash@3
      inputs:
        targetType: 'inline'
        script: 'sed -i -E "s/\"version\"\:\ \"([0-9]*).([0-9]*).([0-9]*)\"/\"version\"\:\ \"\1.\2.$(Build.BuildId)\"/" package.json'
      displayName: 'Set version to BuildId'

    - script: 'yarn build'
      displayName: 'Build React App'

    - task: Npm@1
      inputs:
        command: 'publish'
        workingDir: './'
        verbose: true
        publishRegistry: 'useFeed'
        publishFeed: '40fc911e-43e1-4746-bf4c-af2ba93b4046/9cedcfd0-83c6-4f2b-b93a-73c5e3aa4f2d'
      displayName: 'Publish to Azure Artifacts'