trigger: none

resources:
  repositories:
  - repository: devops-common
    type: git
    name: OneTalent/devops-common
    ref: main

  pipelines:
  - pipeline: build-pipeline
    source: build-onetalent-ui-kit-storybook
    trigger: 
      branches:
        include: 
        - main

variables: 
- name: migration_arg
  value: ''

stages:
- template: pipelines/templates/helm-deploy-all-envs.template.yml@devops-common
  parameters:
    application_name: onetalent-ui-kit
    build_output_name: build-pipeline
    config_map_path: "helm"
    envs: 
      dev:
        trigger: automatic
      stg:
        trigger: manual
    additional_deploy_params: >-
      --set azureAd.tenantId=$(tenantId)
      --set azureAd.clientId=$(clientId)
      --set azureAd.clientApiId=$(clientApiId)
      $(migration_arg)