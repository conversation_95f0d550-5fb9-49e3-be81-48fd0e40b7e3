trigger:
  branches:
    include:
      - main
      - release/*
  paths:
    include:
      - src/*
    exclude:
      - devops/*

variables:
  MODULE_TYPE: "admin"

parameters:
  - name: env
    type: string
    default: dev
    values:
      - dev
      - stg
      - uat
      - uat2
      - preview
      - prd

resources:
  repositories:
    - repository: devops-common
      type: git
      name: OneTalent/devops-common
      ref: main

stages:
  - template: pipelines/templates/frontend-build.template.yml@devops-common
    parameters:
      env: ${{ parameters.env }}
      service_name: "fe-admin-web"
      repository_name: "fe-admin-web"
      dockerfile_path: "Dockerfile"
      helm_chart_folder: "helm"
      version_prefix: "1.0"
