{"Header": {"Header 1 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "36px", "fontWeight": 500, "letterSpacing": "-0.72px", "lineHeight": "normal", "textTransform": "none", "textDecoration": "none"}}, "Header 1 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "36px", "fontWeight": 400, "letterSpacing": "-0.72px", "lineHeight": "normal", "textTransform": "none", "textDecoration": "none"}}, "Header 1 Light": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "36px", "fontWeight": 300, "letterSpacing": "-0.72px", "lineHeight": "normal", "textTransform": "none", "textDecoration": "none"}}, "Header 2 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "28px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "34px", "textTransform": "none", "textDecoration": "none"}}, "Header 2 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "28px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "34px", "textTransform": "none", "textDecoration": "none"}}, "Header 3 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "24px", "fontWeight": 500, "letterSpacing": "-0.24px", "lineHeight": "30px", "textTransform": "none", "textDecoration": "none"}}, "Header 3 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "24px", "fontWeight": 400, "letterSpacing": "-0.24px", "lineHeight": "30px", "textTransform": "none", "textDecoration": "none"}}, "Header 3 Light": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "24px", "fontWeight": 300, "letterSpacing": "-0.24px", "lineHeight": "30px", "textTransform": "none", "textDecoration": "none"}}}, "Body": {"Body XL Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "18px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "28px", "textTransform": "none", "textDecoration": "none"}}, "Body XL Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "18px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "28px", "textTransform": "none", "textDecoration": "none"}}, "Body 1 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "16px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "22px", "textTransform": "none", "textDecoration": "none"}}, "Body 1 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "16px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "22px", "textTransform": "none", "textDecoration": "none"}}, "Body 2 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "14px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "18px", "textTransform": "none", "textDecoration": "none"}}, "Body 2 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "14px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "18px", "textTransform": "none", "textDecoration": "none"}}, "Body 3 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "12px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "normal", "textTransform": "none", "textDecoration": "none"}}, "Body 3 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "12px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "normal", "textTransform": "none", "textDecoration": "none"}}, "Body 4 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "11px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "14px", "textTransform": "none", "textDecoration": "none"}}, "Body 4 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "11px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "14px", "textTransform": "none", "textDecoration": "none"}}}, "CTA": {"CTA 1 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "16px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "20px", "textTransform": "capitalize", "textDecoration": "none"}}, "CTA 1 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "16px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "22px", "textTransform": "capitalize", "textDecoration": "none"}}, "CTA 2 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "14px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "normal", "textTransform": "capitalize", "textDecoration": "none"}}, "CTA 2 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "14px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "16px", "textTransform": "capitalize", "textDecoration": "none"}}, "CTA 3 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "12px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "normal", "textTransform": "capitalize", "textDecoration": "none"}}, "CTA 3 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "12px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "normal", "textTransform": "capitalize", "textDecoration": "none"}}}, "Labels": {"Label XL Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "24px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "28px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label XL Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "24px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "28px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label L Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "20px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "24px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label L Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "20px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "24px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label M2 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "18px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "22px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label M2 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "18px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "22px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label M1 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "14px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "16px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label M1 Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "14px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "16px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label S Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "13px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "15px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label S Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "13px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "15px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label XS Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "12px", "fontWeight": 500, "letterSpacing": "0px", "lineHeight": "14px", "textTransform": "capitalize", "textDecoration": "none"}}, "Label XS Regular": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "12px", "fontWeight": 400, "letterSpacing": "0px", "lineHeight": "14px", "textTransform": "capitalize", "textDecoration": "none"}}}, "header": {"Header 3 Medium": {"$type": "typography", "$value": {"fontFamily": "ADNOC Sans", "fontSize": "24px", "fontWeight": 500, "letterSpacing": "-0.24px", "lineHeight": "30px", "textTransform": "none", "textDecoration": "none"}}}}