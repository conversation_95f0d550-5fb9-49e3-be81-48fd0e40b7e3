{"name": "one-talent", "version": "1.4.0", "main": "./src/ts", "author": "Adnoc", "private": true, "sideEffects": ["**/*.css", "**/*.scss"], "scripts": {"setup": "node ./scripts/setup-npmrc-config-universal.js", "start": "webpack serve", "start:dev": "cross-env ENV_FILE=.env.local.dev webpack serve", "start:stg": "cross-env ENV_FILE=.env.local.stg webpack serve", "start:mf": "cross-env MODULE_FEDERATION=on webpack serve", "start:mf:dev:feedback": "cross-env MODULE_TYPE=feedback  cross-env ENV_FILE=.env.local.dev cross-env MODULE_FEDERATION=on cross-env FE_APP_PORT=3010 webpack serve", "start:mf:stg:feedback": "cross-env MODULE_TYPE=feedback  cross-env ENV_FILE=.env.local.stg cross-env MODULE_FEDERATION=on cross-env FE_APP_PORT=3010 webpack serve", "start:mf:uat:feedback": "cross-env MODULE_TYPE=feedback  cross-env ENV_FILE=.env.local.uat cross-env MODULE_FEDERATION=on cross-env FE_APP_PORT=3010 webpack serve", "start:mf:uat2:feedback": "cross-env MODULE_TYPE=feedback  cross-env ENV_FILE=.env.local.uat2 cross-env MODULE_FEDERATION=on cross-env FE_APP_PORT=3010 webpack serve", "start:mf:preview:feedback": "cross-env MODULE_TYPE=feedback  cross-env ENV_FILE=.env.local.preview cross-env MODULE_FEDERATION=on cross-env FE_APP_PORT=3010 webpack serve", "build": "cross-env NODE_ENV=production cross-env MODULE_FEDERATION=on webpack --progress", "build:dev": "cross-env ENV_FILE=.env.dev cross-env MODULE_FEDERATION=on webpack --progress", "build:stg": "cross-env ENV_FILE=.env.stg cross-env MODULE_FEDERATION=on webpack --progress", "build:uat": "cross-env ENV_FILE=.env.uat cross-env MODULE_FEDERATION=on webpack --progress", "build:uat2": "cross-env ENV_FILE=.env.uat2 cross-env MODULE_FEDERATION=on webpack --progress", "build:preview": "cross-env ENV_FILE=.env.preview cross-env MODULE_FEDERATION=on webpack --progress", "build:prd": "cross-env ENV_FILE=.env.prd cross-env MODULE_FEDERATION=on webpack --progress", "lint": "eslint --debug ./src", "lint:fix": "eslint --debug ./src --fix", "prettier": "prettier \"**/*.+(js|jsx|json|css|md)\"", "prettier:write": "prettier --write \"**/*.+(js|jsx|json|css|md)\"", "test": "jest --runInBand", "test:ci": "jest --coverage", "typecheck": "tsc --noEmit --project ./tsconfig.json", "test:watch": "jest --passWithNoTests --watch", "generate": "yarn generate:query-service && yarn generate:performance && yarn generate:feedback && yarn generate:atr && yarn generate:career-development", "generate:remote": "yarn generate:query-service:remote && yarn generate:performance:remote && yarn generate:atr:remote && yarn generate:career-development:remote", "generate:performance": "podman run -v ./src/ts/performance/api:/out -it --rm docker.io/openapitools/openapi-generator-cli generate -i /out/swagger.json -g typescript-fetch --additional-properties=modelPropertyNaming=original -o /out/generated  --skip-validate-spec", "generate:feedback": "podman run -v ./src/ts/feedback/api:/out -it --rm docker.io/openapitools/openapi-generator-cli generate -i /out/swagger.json -g typescript-fetch --additional-properties=modelPropertyNaming=original -o /out/generated  --skip-validate-spec", "swagger:feedback": "curl -L -o ./src/ts/feedback/api/swagger.json https://api.dev.onetalent.adnoc.ae/feedback/swagger/PublicAPI/swagger.json", "swagger:performance": "curl -L -o ./src/ts/performance/api/swagger.json https://api.dev.onetalent.adnoc.ae/performance/swagger/PublicAPI/swagger.json", "generate:performance:remote": "yarn swagger:performance && yarn generate:performance", "generate:feedback:remote": "yarn swagger:feedback && yarn remove:generated-feedback && yarn generate:feedback", "remove:generated-feedback": "rimraf ./src/ts/feedback/api/generated", "sync-styles": "ts-node ./tailwind/scripts/main.ts", "postinstall": "patch-package", "prepare": "husky install", "proxy:fe": "mitmproxy --ssl-insecure --listen-port 8081 -s ./mitmproxy/proxy-fe.py"}, "dependencies": {"@ali-tas/htmldiff-js": "2.0.1", "@azure/msal-browser": "3.19.0", "@azure/msal-react": "2.0.20", "@epam/promo": "6.1.2", "@epam/uui": "6.1.2", "@epam/uui-components": "6.1.2", "@epam/uui-core": "6.1.2", "@hookform/resolvers": "3.9.0", "@microsoft/applicationinsights-web": "3.3.1", "@oh/assets": "2.3.0", "@oh/components": "40.0.0", "@oh/constants": "2.1.1", "@oh/contracts": "17.1.0-next.1", "@oh/hooks": "39.0.0", "@oh/services": "10.5.1", "@oh/utils": "5.0.0", "@ot/onetalent-ui-kit": "5.0.968469", "@tailwindcss/container-queries": "0.1.1", "@tanstack/react-query": "5.52.2", "@tanstack/react-query-devtools": "5.40.1", "@testing-library/react-hooks": "8.0.1", "clsx": "1.1.1", "dayjs": "1.11.11", "dompurify": "3.2.4", "eslint-plugin-local-rules": "3.0.2", "history": "5.3.0", "html-diff-ts": "1.4.2", "http-status-codes": "2.3.0", "lodash": "4.17.21", "onehub-pdf-wasm": "0.1.6", "path-browserify": "1.0.1", "postcss-import": "16.1.0", "react": "18.2.0", "react-day-picker": "9.6.3", "react-dom": "18.2.0", "react-helmet": "6.1.0", "react-hook-form": "7.54.2", "react-infinite-scroller": "1.2.6", "react-router-dom": "6.4.2", "rimraf": "6.0.1", "sanitize-html": "2.13.1", "single-spa-react": "6.0.1", "swiper": "8.4.5", "workbox-cacheable-response": "7.1.0", "workbox-expiration": "7.1.0", "workbox-precaching": "7.1.0", "workbox-routing": "7.1.0", "workbox-strategies": "7.1.0", "yup": "1.4.0"}, "devDependencies": {"@babel/plugin-transform-runtime": "7.24.6", "@babel/preset-env": "7.24.6", "@babel/preset-react": "7.24.6", "@babel/runtime": "7.24.7", "@babel/runtime-corejs3": "7.24.7", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@epam/uui-test-utils": "6.1.2", "@pmmmwh/react-refresh-webpack-plugin": "0.5.15", "@svgr/webpack": "8.1.0", "@tanstack/eslint-plugin-query": "5.43.1", "@teamsupercell/typings-for-css-modules-loader": "2.5.2", "@testing-library/dom": "10.1.0", "@testing-library/jest-dom": "6.4.6", "@testing-library/react": "16.0.0", "@testing-library/user-event": "14.5.2", "@types/circular-dependency-plugin": "5.0.8", "@types/dompurify": "3.0.5", "@types/dotenv-webpack": "7.0.7", "@types/jest": "29.5.12", "@types/node": "20.14.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-helmet": "6.1.11", "@types/react-infinite-scroller": "1.2.5", "@types/sanitize-html": "2.13.0", "@types/webpack-assets-manifest": "5.1.4", "@types/webpack-env": "1.18.5", "@typescript-eslint/eslint-plugin": "5.60.0", "@typescript-eslint/parser": "5.60.0", "autoprefixer": "10.4.19", "babel-jest": "29.7.0", "babel-loader": "9.1.3", "circular-dependency-plugin": "5.2.2", "compression-webpack-plugin": "11.1.0", "copy-webpack-plugin": "12.0.2", "cross-env": "7.0.3", "css-loader": "7.1.2", "dotenv": "16.4.5", "dotenv-webpack": "8.1.0", "env-cmd": "10.1.0", "eslint": "8.57.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-prettier": "5.0.0", "eslint-plugin-react": "7.29.4", "eslint-plugin-react-hooks": "4.5.0", "eslint-plugin-unicorn": "42.0.0", "html-webpack-plugin": "5.6.0", "husky": "9.1.6", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "mini-css-extract-plugin": "2.9.0", "patch-package": "8.0.0", "postcss": "8.4.47", "postcss-loader": "8.1.1", "postcss-preset-env": "9.5.14", "prettier": "3.0.0", "prettier-plugin-tailwindcss": "0.6.5", "raw-loader": "4.0.2", "react-refresh": "0.14.2", "remove-files-webpack-plugin": "1.5.0", "sass": "1.77.4", "sass-loader": "14.2.1", "style-loader": "4.0.0", "tailwind-scrollbar": "4.0.2", "tailwindcss": "3.4.13", "terser-webpack-plugin": "5.3.10", "ts-jest": "29.1.4", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths-webpack-plugin": "4.1.0", "typescript": "5.8.2", "uglify-js": "3.17.4", "webpack": "5.98.0", "webpack-assets-manifest": "5.2.1", "webpack-cli": "5.1.4", "webpack-dev-server": "5.0.4", "webpack-merge": "6.0.1", "webpack-pwa-manifest": "4.3.0", "workbox-webpack-plugin": "7.1.0"}, "resolutions": {"string-width": "4.2.3", "strip-ansi": "6.0.1", "wrap-ansi": "7.0.0"}}