import { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';

import { theme } from './tailwind';

export default {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './node_modules/@ot/onetalent-ui-kit/**/*.{js,jsx,ts,tsx}'
  ],
  safelist: [],
  theme: {
    extend: {
      boxShadow: {
        oh_light: '0px -8px 16px 0px #00000014',
        dropdown: '0px 4px 12px 0px rgba(0, 0, 0, 0.08)',
        card: '0px 12px 20px 0px rgba(0, 0, 0, 0.08)',
        action_bar: '0px 10px 50px 0px rgba(0, 0, 0, 0.25)',
        jd_short_info_bar: '0px 12px 15px 0 rgba(0, 0, 0, 0.26)',
        action_footer: '0px -8px 16px 0px rgba(0, 0, 0, 0.08)',
        suggested_skill_card: '0px 12px 30px 0px rgba(0, 0, 0, 0.12)',
        atr_summary_card: '0px 24px 40px 0px rgba(0, 0, 0, 0.12)',
        objectives_summary_card: '0px 12px 16px 0px rgba(0, 0, 0, 0.12)',
        arrows: '0px 8px 16px 0px rgba(0, 0, 0, 0.12)'
      },
      colors: {
        ...theme?.colors,
        'background--alice-blue': 'var(--approvals--item-active)',
        'background--dark-stroke': 'var(--background--dark-stroke)',
        'background--info': 'var(--background--info)',
        'background--mid-gray': 'var(--background--mid-gray)',
        'background--primary': 'var(--background--primary)',
        'background--subtle': 'var(--background--subtle)',
        'background-input-active': 'var(--background-input-active)',
        'background-light-stroke': 'var(--background--light-stroke)',
        'blue-active': 'var(--blue-active)',
        'blue-core': 'var(--blue-core)',
        'background--primary--rgb': 'var(--background--primary--rgb)',
        'background--primary-60': 'rgba(var(--background--primary--rgb), 0.6)',
        'border-info': 'var(--border-info)',
        'divider--primary': 'var(--divider--primary)',
        'divider--secondary': 'var(--divider--secondary)',
        'foreground--info': 'var(--foreground--info)',
        'icons-background': 'var(--icons-background)',
        'mid-info': 'var(--mid--info)',
        'red-core': 'var(--red-core)',
        'typography--primary': 'var(--typography--primary)',
        'typography--secondary': 'var(--typography--secondary)',
        'typography--tertiary': 'var(--typography--tertiary)',
        ai_bg: '#F0EBF6'
      },
      screens: {
        sm: '376px',
        md: '701px',
        '2xl': '1440px',
        '3xl': '1600px',
        '4xl': '1920px',
        '5xl': '2880px'
      },
      containers: {
        sm: '376px',
        md: '701px',
        '2xl': '1440px',
        '3xl': '1600px',
        '4xl': '1920px',
        '5xl': '2880px'
      },
      fontSize: theme?.typography
    },
    spacing: {
      ...theme?.spacing
    },
    fontFamily: {
      'adnoc-sans': ["'ADNOC Sans'"]
    },
    plugins: [
      plugin(({ addComponents }) => {
        addComponents({
          '.date-picker-error': {
            '& > input': {
              '@apply !border-2 !border-solid !border-[var(--red-core)]': {}
            }
          }
        });
      })
    ]
  },
  variants: {
    extend: {
      backgroundColor: ['supports-hover:hover'],
      textColor: ['supports-hover:hover']
    }
  },
  plugins: [
    require('tailwind-scrollbar'),
    plugin(({ addVariant }) => {
      addVariant('dark-mode', '[data-theme="dark"] &');
      addVariant('supports-hover', '@media (hover: hover) and (pointer: fine)');
    }),
    require('@tailwindcss/container-queries')
  ]
} satisfies Config;
